import os

# --- API & Model Configuration ---
LM_STUDIO_URL = os.environ.get("LM_STUDIO_URL", "http://127.0.0.1:1234/v1")
LM_STUDIO_API_KEY = os.environ.get("LM_STUDIO_API_KEY", "lm-studio-key")
LM_STUDIO_MODEL_NAME = os.environ.get("LM_STUDIO_MODEL_NAME", "gemma-3-12b-it")  # Model for decisions, query rewriting, etc.
OLLAMA_URL = os.environ.get("OLLAMA_URL", "http://127.0.0.1:11434/v1")
OLLAMA_MODEL_NAME = os.environ.get("OLLAMA_MODEL_NAME", "luna-small:latest")  # Main model for conversations
KOKORO_BASE_URL = os.getenv("KOKORO_FASTAPI_URL", "http://127.0.0.1:8880") + "/v1"
DECISION_MODEL = "gemma-3-4b-it-qat"  # Model for decision making
REWRITE_MODEL = "gemma-3-1b-it-qat" # Model for RAG query rewriting

# LM Studio Vision (Optional)
LM_STUDIO_VISION_URL = os.environ.get("LM_STUDIO_VISION_URL")
LM_STUDIO_VISION_API_KEY = os.environ.get("LM_STUDIO_VISION_API_KEY", "lm-studio-vision-key")
VISION_MODEL = os.environ.get("LM_STUDIO_VISION_MODEL")

# --- Channel IDs ---
TRANSCRIPT_LOG_CHANNEL_ID = 1369158552996024370
CALL_NOTIFICATION_CHANNEL_ID_STR = os.getenv("CALL_NOTIFICATION_CHANNEL_ID")
PROMPT_LOG_CHANNEL_ID = 1369178090995187783

# --- Database ---
DB_PATH = "conversation_logs.db" # Define DB Path globally (Consider making this configurable)

# --- Command Settings ---
SCREENSHOT_COOLDOWN = 1  # seconds

# --- User Profile Mapping ---
# Maps Discord User IDs to user profiles
# Each profile contains a 'main_name' (how Luna refers to them)
# and a list of 'aliases' (other names they might use, MUST be lowercase)
USER_PROFILES = {
    443964602850738176: { # Tachi / Sir Goonlord / Goonlord / Guy
        "main_name": "Tachi",
        "aliases": ["tachi", "sir goonlord", "goonlord",] # Added 'guy' and 'gavin'
    },
     987654321098765432: { # Ethan (Placeholder ID - REMOVE or REPLACE)
         "main_name": "Ethan",
         "aliases": ["ethan", "UwU"]
     },
    464688859486224394: { # Toast / Mari
        "main_name": "Mari", # Assuming Mari is the preferred main name
        "aliases": ["toast", "mari", "marty"]
    },
    443182881737670666: { # Jaden / Jayden
        "main_name": "Jaden",
        "aliases": ["jaden", "jayden"]
    },
    297176773140152321: { # Zack / Zach
        "main_name": "Zack",
        "aliases": ["zack", "zach"]
    },
    750136646074105938: { # Live / Liv
        "main_name": "Liv",
        "aliases": ["live", "liv"]
    },
    921637353364287489: { # Live / Liv
        "main_name": "Gavin",
        "aliases": ["test", "gavin"]
    },
    745467055607644221: { # Live / Liv
        "main_name": "Max",
        "aliases": ["Max", "Maxforce12"]
    },
}

# --- Helper Functions for User Profiles ---

def get_main_name_from_alias(alias: str) -> str | None:
    """
    Finds the main name associated with a given alias.
    Returns the original alias if no profile is found.
    """
    alias_lower = alias.lower()
    for _user_id, profile in USER_PROFILES.items():
        if alias_lower in profile.get("aliases", []):
            return profile.get("main_name")
    # If no alias matches, return the original alias capitalized
    # This handles cases where a new person joins or someone uses an unlisted name
    # If no alias matches, return the original alias capitalized
    # This handles cases where a new person joins or someone uses an unlisted name
    return alias.capitalize()

def get_id_from_alias(alias: str) -> int | None:
    """Finds the Discord User ID associated with a given alias."""
    alias_lower = alias.lower()
    for user_id, profile in USER_PROFILES.items():
        if alias_lower in profile.get("aliases", []):
            return user_id
    return None

def get_profile_by_id(user_id: int) -> dict | None:
    """Gets the user profile dictionary by Discord User ID."""
    return USER_PROFILES.get(user_id)

def get_main_name_by_id(user_id: int) -> str | None:
    """
    Gets the main name directly by Discord User ID.
    Returns None if the ID is not found.
    """
    profile = USER_PROFILES.get(user_id)
    if profile:
        return profile.get("main_name")
    return None

# --- Deprecated User Alias Mapping ---
# USER_ALIASES = { ... } # Kept for reference, but functionally replaced by USER_PROFILES

# --- Prompt Settings ---
MAX_HISTORY_MESSAGES = 15 # Limit history for LLM context
RAG_TOP_K = 50 # How many documents to retrieve initially
RAG_CONTEXT_COUNT = 15 # How many retrieved documents to include in the final prompt

# --- Response Generation ---
DEFAULT_TEMP = 1.0
TEMP_PARTICIPATION_INCREMENT = 0.1
MAX_TEMP = 1.0 # Capped temperature for Vertex AI
MAX_OUTPUT_TOKENS = 300
TOP_P = 0.85

# --- DM Generation ---
DM_MAX_OUTPUT_TOKENS = 150
DM_TEMP = 0.85

# --- Call Message Generation ---
CALL_MSG_MAX_OUTPUT_TOKENS = 50
CALL_MSG_TEMP = 0.75

# --- Image Analysis ---
IMAGE_ANALYSIS_MAX_TOKENS = 150
IMAGE_ANALYSIS_TEMP = 0.2
IMAGE_DOWNLOAD_TIMEOUT = 10.0

# --- Decision Making ---
DECISION_MAX_TOKENS = 20
DECISION_TEMP = 0.1
DECISION_CONFIDENCE_THRESHOLD = 85  # Minimum confidence percentage required to respond

# --- Query Rewriting ---
REWRITE_MAX_TOKENS = 50
REWRITE_TEMP = 0.7