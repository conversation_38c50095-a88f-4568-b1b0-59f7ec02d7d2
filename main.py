import discord
from discord.ext import commands
import asyncio
import os
import logging
import sqlite3
import time  # Add this import for timing
import torch  # Add this import for CUDA warmup
from load_environment import load_env_vars
from speech_to_text import transcribe_audio, WHISPER_MODEL
from discord_sink import DiscordSink
from text_to_speech import synthesize_speech # Corrected import path
from rag_retriever import RAGRetriever
import llm_response # Import the module directly
import re
import numpy as np # Add numpy import
from text_to_speech import close_tts_engine
# from gemini_vision import analyze_image_url # Assuming handled by llm_response now
from llm_brain import get_brain_decision # Import the brain function
# Import alias lookup functions
from llm_response.config import get_id_from_alias, get_main_name_from_alias
# No need to import VertexAI modules anymore
# --- Setup Logging ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

load_env_vars()

# Initialize LM Studio after loading environment variables
llm_response.initialize_lm_studio()

# Initialize Ollama for response generation
llm_response.initialize_ollama()

# --- Environment Variables ---
DISCORD_BOT_TOKEN = os.environ.get("DISCORD_BOT_TOKEN")
GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")
KOKORO_FASTAPI_URL = os.environ.get("KOKORO_FASTAPI_URL")
KOKORO_VOICE = os.environ.get("KOKORO_VOICE")

if DISCORD_BOT_TOKEN is None:
    raise ValueError("DISCORD_BOT_TOKEN environment variable not set.")
if GOOGLE_API_KEY is None:
    raise ValueError("GOOGLE_API_KEY environment variable not set.")

# --- CUDA Warmup Function ---
def warmup_cuda():
    logger.info("Warming up CUDA...")
    try:
        if torch.cuda.is_available():
            start_time = time.monotonic()
            # Perform a small operation and synchronize
            a = torch.randn(10, 10, device='cuda')
            b = torch.randn(10, 10, device='cuda')
            c = a @ b
            torch.cuda.synchronize()
            end_time = time.monotonic()
            logger.info(f"CUDA warmup completed in {end_time - start_time:.4f}s")
        else:
            logger.warning("CUDA not available, skipping warmup.")
    except Exception as e:
        logger.error(f"CUDA warmup failed: {e}")

# --- Transcription Warmup Function ---
def warmup_transcription_model():
    logger.info("Warming up transcription model (faster-whisper)...")
    try:
        if torch.cuda.is_available(): # Only run if CUDA is used by the model
            start_time = time.monotonic()
            # Create a tiny silent audio sample (e.g., 0.1 seconds of zeros)
            # Needs to be float32, 16kHz mono
            dummy_audio = np.zeros(int(16000 * 0.1), dtype=np.float32)
            # Run a dummy transcription
            _ = list(WHISPER_MODEL.transcribe(dummy_audio, beam_size=1)[0]) # Consume the generator
            torch.cuda.synchronize() # Ensure it finishes
            end_time = time.monotonic()
            logger.info(f"Transcription model warmup completed in {end_time - start_time:.4f}s")
        else:
            logger.info("Transcription model warmup skipped (CUDA not available or model not on CUDA).")
    except Exception as e:
        logger.error(f"Transcription model warmup failed: {e}")

# --- Intents ---
intents = discord.Intents.default()
intents.message_content = True # Explicitly enable message content
intents.voice_states = True    # Explicitly enable voice states
intents.members = True         # Explicitly enable server members intent

bot = commands.Bot(command_prefix='!', intents=intents)

# Dictionary to store active text chat sessions {channel_id: ChatSession}
text_chat_sessions = {}

# Attach the chat sessions dictionary to the bot for global access
bot.chat_sessions = {}

# --- Database Path ---
DB_PATH = "conversation_logs.db" # Define DB Path globally

# Database logging is now handled by llm_response.log_message_to_db
# --- System Prompt Setup ---
try:
    with open('system_prompt.txt', 'r') as f:
        SYSTEM_PROMPT = f.read()
except Exception as e:
    logger.error(f"Error loading system prompt: {e}")
    SYSTEM_PROMPT = ""

@bot.event
async def on_ready():
    logger.info(f'Logged in as {bot.user.name} ({bot.user.id})')

    # Initialize memory systems and store the instances on the bot for access
    bot.rag_retriever = await initialize_memory_systems()

@bot.command()
async def join(ctx):
    if ctx.author.voice:
        voice_channel = ctx.author.voice.channel
        text_channel = ctx.channel
        try:
            # Connect and get the voice client instance
            vc = await voice_channel.connect()
            # Get the initialized LM Studio client from llm_response module
            lm_studio_client = llm_response.get_lm_studio_client() # Use getter function
            if not lm_studio_client:
                 logger.error("LM Studio client not initialized before !join command.")
                 await ctx.send("Error: Language model is not ready.")
                 await vc.disconnect() # Disconnect if client failed
                 return
            # Pass the voice client (vc) and the LM Studio client to the sink constructor
            sink = DiscordSink(text_channel, bot, vc, lm_studio_client) # Pass LM Studio client
            sink.system_prompt = SYSTEM_PROMPT
            vc.start_recording(sink, lambda *args: None) # Reverted: Use original simple callback
            bot.loop.create_task(sink.check_for_silence())
        except discord.ClientException as e:
            await ctx.send("Error connecting to voice: " + str(e))
    else:
        await ctx.send("You are not in a voice channel.")

@bot.command()
async def leave(ctx):
    if ctx.guild.voice_client:
        if ctx.guild.voice_client.is_playing():
            ctx.guild.voice_client.stop()
        ctx.guild.voice_client.stop_recording()
        await ctx.guild.voice_client.disconnect()
    else:
        await ctx.send("Not in a voice channel.")

@bot.command()
async def link_text(ctx):
    """Link the current text channel with Luna's voice chat"""
    if not ctx.guild.voice_client:
        await ctx.send("I'm not in a voice channel yet. Use !join first.")
        return

    sink = None
    for vc in bot.voice_clients:
        if vc.guild == ctx.guild and hasattr(vc, 'sink'):
            sink = vc.sink
            break

    if sink:
        # Handle different channel types (DMChannel doesn't have a name attribute)
        old_channel = sink.text_channel.name if sink.text_channel and hasattr(sink.text_channel, 'name') else ("DM" if sink.text_channel else "none")
        sink.text_channel = ctx.channel
        await ctx.send(f"✅ Linked text channel changed from #{old_channel} to #{ctx.channel.name}")
    else:
        await ctx.send("I couldn't find my voice connection. Try using !join first.")

@bot.event
async def on_command_error(ctx, error):
    logger.error(f"Command error: {error}")
    await ctx.send(f"Error: {error}")


@bot.event
async def on_message(message):
    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    # Process commands first (like !join, !leave)
    await bot.process_commands(message)

    # Check if the message was handled by process_commands
    ctx = await bot.get_context(message)
    if ctx.command is not None:
        # It was a command, so we don't need to process it as a regular message
        return

    # --- Special handling for DMs ---
    if isinstance(message.channel, discord.DMChannel):
        logger.info(f"Processing DM from {message.author.display_name} ({message.author.id}): {message.content[:50]}...")

        # Get or create DM chat session for statefulness
        dm_session_key = f"dm_{message.author.id}"
        dm_session = bot.chat_sessions.get(dm_session_key)

        if dm_session is None:
            logger.info(f"Creating new DM chat session for user {message.author.id}")
            dm_session = {
                "id": f"dm_{message.author.id}_{int(time.time())}",
                "messages": [{"role": "system", "content": SYSTEM_PROMPT}]
            }
            bot.chat_sessions[dm_session_key] = dm_session

        # Log the DM message to database
        try:
            llm_response.log_message_to_db(
                user_id=message.author.id,
                role="user",
                content=message.content,
                timestamp=message.created_at.timestamp(),
                channel_type="dm",
                channel_id=message.channel.id
            )
        except Exception as log_err:
            logger.error(f"Error logging DM message: {log_err}", exc_info=True)

        # Process the DM using Ollama with stateful conversation
        try:
            await llm_response.process_user_message(
                bot=bot,
                text=message.content,
                user_id=message.author.id,
                conversation_history=None,  # History managed by session
                system_prompt=SYSTEM_PROMPT,
                sink=None,  # No sink for DMs
                force_respond=True,  # Always respond to DMs
                display_name=message.author.display_name,
                text_channel=message.channel,
                image_analysis_text=None,  # TODO: Add image analysis for DMs if needed
                chat_session=dm_session  # Pass the DM session for statefulness
            )
        except Exception as dm_err:
            logger.error(f"Error processing DM from {message.author.display_name}: {dm_err}", exc_info=True)
            await message.channel.send("sorry, i had trouble processing that dm")

        return  # Exit early for DMs, don't process through regular guild message logic

    # --- Check for Image Mention ---
    image_processed = False
    if message.attachments and "luna" in message.content.lower():
        image_url = None
        for attachment in message.attachments:
            # Basic check for image content types or extensions
            if attachment.content_type and attachment.content_type.startswith("image/"):
                image_url = attachment.url
                break # Process first image found
            elif attachment.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                image_url = attachment.url
                break # Process first image found

        if image_url:
            logger.info(f"Processing image mention in channel {message.channel.id} from {message.author.display_name}. Image URL: {image_url}")
            try:
                # Call a new function in llm_response to handle image processing
                 # Pass the process_user_message function to handle potential circular dependency
                 await llm_response.process_image_mention(
                     message=message,
                     image_url=image_url,
                     bot=bot,
                     system_prompt=SYSTEM_PROMPT,
                     process_user_message_func=llm_response.process_user_message # Pass the function
                 )
                 image_processed = True # Mark as processed to skip regular text handling
            except AttributeError:
                 logger.error("llm_response.process_image_mention function not found yet. Please implement it.")
            except Exception as img_err:
                 logger.error(f"Error processing image mention: {img_err}", exc_info=True)
                 await message.channel.send("Sorry, I had trouble looking at that image.")

    # --- Process Regular Text Message (if not an image mention) ---
    if not image_processed:
        logger.info(f"Processing regular text message in channel {message.channel.id}: {message.content[:50]}...")

        # --- Check for Image Attachments & Analyze Immediately ---
        attached_image_url = None
        image_analysis_result = None # Store analysis result here
        if message.attachments:
            for attachment in message.attachments:
                is_image = False
                if attachment.content_type and attachment.content_type.startswith("image/"):
                    is_image = True
                elif attachment.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                    is_image = True

                if is_image:
                    attached_image_url = attachment.url
                    logger.info(f"Found image attachment URL: {attached_image_url}. Analyzing immediately...")
                    try:
                        # Call analysis function (ensure it's imported: from llm_response import analyze_image_url_lm_studio)
                        image_analysis_result = await llm_response.analyze_image_url_lm_studio(attached_image_url)
                        if image_analysis_result:
                             logger.info("Image analysis successful.")
                        else:
                             logger.warning("Image analysis returned None.")
                    except Exception as analysis_err:
                         logger.error(f"Error analyzing attached image {attached_image_url}: {analysis_err}", exc_info=True)
                         image_analysis_result = "[Image analysis failed]" # Note failure
                    break # Process first image only

        # --- Log the text message AND image analysis (if any) to the database ---
        try:
            # Log original text message first
            llm_response.log_message_to_db( # Use imported function
                user_id=message.author.id,
                role="user",
                content=message.content, # Log original text content
                timestamp=message.created_at.timestamp(),
                channel_type="text",
                channel_id=message.channel.id
            )
            # Log image analysis separately if it exists
            if image_analysis_result:
                 llm_response.log_message_to_db( # Use imported function
                      user_id=message.author.id, # Associate with the same user
                      role="image_analysis", # Use a distinct role
                      content=f"[Analysis of {attached_image_url}]: {image_analysis_result}", # Log analysis text
                      timestamp=message.created_at.timestamp() + 0.001, # Slightly later timestamp
                      channel_type="text",
                      channel_id=message.channel.id
                 )
        except Exception as log_err:
            logger.error(f"Error during text/image analysis message logging: {log_err}", exc_info=True)
            # Continue processing even if logging fails

        # --- Call LLM Brain for Decision Making ---
        brain_action_taken = False
        brain_decision = None # Initialize brain_decision to handle potential errors before assignment
        try: # This try block covers brain query and action execution
            # --- Preprocess message content for brain: Resolve Aliases ---
            processed_message_content = message.content
            # Simple regex to find potential target names after action verbs
            # This is basic and might need refinement for complex sentences
            potential_target_match = re.search(r"(?:dm|tell|ask|kick|disconnect|call)\s+@?([\w\s]+)", message.content, re.IGNORECASE)
            if potential_target_match:
                potential_alias = potential_target_match.group(1).strip().lower()
                logger.debug(f"Potential target alias found in message: '{potential_alias}'")
                resolved_main_name = get_main_name_from_alias(potential_alias)
                # Check if resolution happened AND it's different from the original (case-insensitive check might be needed if main names can be aliases)
                # Let's assume resolved_main_name returns capitalized main name or capitalized original if no match
                if resolved_main_name and resolved_main_name.lower() != potential_alias:
                    logger.info(f"Resolved alias '{potential_alias}' to main name '{resolved_main_name}' for brain input.")
                    # Replace the *first* occurrence of the alias in the original message content
                    # This is a simple replacement, might replace unintended parts if alias is common word
                    # Consider more robust replacement if needed (e.g., only after the verb)
                    processed_message_content = message.content.replace(potential_target_match.group(1), resolved_main_name, 1)
                    logger.debug(f"Processed message content for brain: '{processed_message_content}'")
                else:
                    logger.debug(f"No specific main name found for potential alias '{potential_alias}', using original.")

            # Construct the user part of the prompt for the brain using processed content
            # TODO: Improve context (history, channel topic, etc.) later in Phase 3
            channel_description = f"'#{message.channel.name}'" if hasattr(message.channel, 'name') else "DM"
            user_prompt_content = f"User '{message.author.display_name}' ({message.author.id}) in channel {channel_description} ({message.channel.id}) said: '{processed_message_content}'" # Use processed content
            if image_analysis_result:
                user_prompt_content += f"\n\nImage Analysis: {image_analysis_result}"

            logger.info(f"Querying LLM Brain for decision on message ID {message.id}...")
            # Now get_brain_decision is async, so we await it directly
            brain_decision = await get_brain_decision(user_prompt_content)
            logger.info(f"LLM Brain Decision for message ID {message.id}: {brain_decision}")

            # --- Parse Brain Decision and Execute Action ---
            decision_lower = brain_decision.lower()

            # Example: Disconnect Action
            disconnect_match = re.search(r"(disconnect|kick)\s+(.*)", decision_lower) # Capture everything after keyword
            if disconnect_match:
                full_identifier_string = disconnect_match.group(2).strip() # e.g., "user gavin (921...)" or "<@921...>" or "gavin"
                logger.info(f"Brain decided to disconnect based on: '{full_identifier_string}'")

                target_member = None
                user_id_to_fetch = None
                name_to_search = None
                log_identifier = full_identifier_string # Default identifier for logs/messages

                # 1. Try extracting mention ID
                mention_id_match = re.search(r"<@!?(\d+)>", full_identifier_string)
                if mention_id_match:
                    try:
                        user_id_to_fetch = int(mention_id_match.group(1))
                        log_identifier = f"Mention ID {user_id_to_fetch}"
                    except ValueError:
                        logger.error(f"Invalid ID format in mention: {mention_id_match.group(1)}")

                # 2. If no mention ID, try extracting plain ID (17+ digits, possibly in brackets/parentheses)
                if not user_id_to_fetch:
                    plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", full_identifier_string) # Look for 17+ digits, optional brackets/parens
                    if plain_id_match:
                        try:
                            user_id_to_fetch = int(plain_id_match.group(1))
                            log_identifier = f"Plain ID {user_id_to_fetch}"
                        except ValueError:
                            logger.error(f"Invalid ID format in plain ID: {plain_id_match.group(1)}")

                # --- Find Member ---
                # a. Try fetching by ID if found
                if user_id_to_fetch:
                    try:
                        logger.debug(f"Attempting to fetch member by ID: {user_id_to_fetch}")
                        target_member = await message.guild.fetch_member(user_id_to_fetch)
                    except (discord.NotFound, discord.HTTPException) as e:
                        logger.warning(f"Failed to fetch member by ID {user_id_to_fetch}: {e}")
                        target_member = None # Ensure it's None if fetch fails

                # b. Fallback: Try fetching by name (using the cleaned full string if no ID was used)
                if not target_member:
                    # Clean the original string for use as a name
                    name_to_search = re.sub(r'[<@!>#()\[\]]', '', full_identifier_string).strip() # Clean more chars
                    # Remove common prefixes like "user " if present
                    if name_to_search.lower().startswith("user "):
                        name_to_search = name_to_search[5:].strip()

                    if name_to_search: # Proceed only if name is not empty after cleaning
                        log_identifier = f"Name '{name_to_search}'" # Update log identifier if using name
                        try:
                            logger.debug(f"Attempting to get member by cleaned name: {name_to_search}")
                            target_member = message.guild.get_member_named(name_to_search)
                            # TODO: Could add smarter fuzzy matching or iteration over members if needed
                        except Exception as e: # Catch potential errors during name lookup
                            logger.error(f"Error during get_member_named for '{name_to_search}': {e}")
                            target_member = None
                    else:
                        # If cleaning resulted in empty string, don't attempt name search
                        logger.warning(f"Cleaned identifier '{full_identifier_string}' resulted in empty name string.")
                        log_identifier = full_identifier_string # Revert log identifier

                # --- Execute Disconnect ---
                if target_member:
                    if target_member.voice and target_member.voice.channel:
                         try:
                             await target_member.move_to(None, reason=f"Action requested by LLM Brain based on {message.author.display_name}'s message.")
                             await message.channel.send(f"✅ Disconnected {target_member.mention} as requested by the brain.")
                             logger.info(f"Successfully disconnected {target_member.display_name} ({target_member.id})")
                             brain_action_taken = True
                         except discord.Forbidden:
                             logger.warning(f"Missing permissions to disconnect {target_member.display_name}")
                             await message.channel.send(f"⚠️ I don't have permission to disconnect {target_member.mention}.")
                         except discord.HTTPException as e:
                             logger.error(f"Failed to disconnect {target_member.display_name}: {e}")
                             await message.channel.send(f"❌ Failed to disconnect {target_member.mention}.")
                    else:
                         logger.info(f"User {target_member.display_name} ({target_member.id}) not in a voice channel to disconnect.") # Use target_member details
                         await message.channel.send(f"ℹ️ {target_member.mention} isn't in a voice channel.")
                         # Decide if this counts as 'action taken' - maybe not? Let's say no for now.
                else:
                    # Use the identifier we tried to search with (ID or cleaned name)
                    logger.warning(f"Could not find user matching '{log_identifier}' to disconnect.")
                    await message.channel.send(f"❓ Couldn't find user matching '{log_identifier}' to disconnect.")

                brain_action_taken = True # Mark action attempted (even if finding/disconnect failed)

            # Example: DM Action
            # Regex to find identifier and message content after "dm"
            dm_match = re.search(r"dm\s+(.*)\s+(?:saying|tell(?:ing)?\s+(?:him|her|them)|with)\s*['\"]?(.*)['\"]?", decision_lower, re.IGNORECASE | re.DOTALL)
            if dm_match and not brain_action_taken: # Only process if disconnect didn't happen
                full_identifier_string = dm_match.group(1).strip()
                message_content = dm_match.group(2).strip()
                logger.info(f"Brain decided to DM based on '{full_identifier_string}' with message: '{message_content}'")

                target_user = None
                user_id_to_fetch = None
                name_to_search = None # Not directly used for fetch_user, but for logging/context
                log_identifier = full_identifier_string

                # 1. Try extracting mention ID
                mention_id_match = re.search(r"<@!?(\d+)>", full_identifier_string)
                if mention_id_match:
                    try:
                        user_id_to_fetch = int(mention_id_match.group(1))
                        log_identifier = f"Mention ID {user_id_to_fetch}"
                    except ValueError:
                        logger.error(f"Invalid ID format in mention for DM: {mention_id_match.group(1)}")

                # 2. If no mention ID, try extracting plain ID
                if not user_id_to_fetch:
                    plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", full_identifier_string)
                    if plain_id_match:
                        try:
                            user_id_to_fetch = int(plain_id_match.group(1))
                            log_identifier = f"Plain ID {user_id_to_fetch}"
                        except ValueError:
                            logger.error(f"Invalid ID format in plain ID for DM: {plain_id_match.group(1)}")

                # --- Find User ID (Prioritize ID lookup) ---
                # a. Try fetching by ID (global lookup)
                # 3. If no ID extracted yet, try alias lookup
                if not user_id_to_fetch:
                    # Clean the identifier string for alias lookup
                    alias_to_search = re.sub(r'[<@!>#()\[\]]', '', full_identifier_string).strip().lower()
                    if alias_to_search.startswith("user "):
                         alias_to_search = alias_to_search[5:].strip()

                    if alias_to_search: # Only search if alias string is not empty
                        logger.debug(f"Attempting alias lookup for DM target: '{alias_to_search}'")
                        found_id_from_alias = get_id_from_alias(alias_to_search)
                        if found_id_from_alias:
                            user_id_to_fetch = found_id_from_alias
                            log_identifier = f"Alias '{alias_to_search}' (ID: {user_id_to_fetch})"
                            logger.info(f"Found user ID {user_id_to_fetch} via alias '{alias_to_search}'.")
                        else:
                             logger.debug(f"Alias '{alias_to_search}' not found in USER_PROFILES.")
                    else:
                        logger.warning(f"Cleaned identifier '{full_identifier_string}' resulted in empty string for alias lookup.")
                        log_identifier = full_identifier_string # Revert log identifier if cleaning failed

                # --- Fetch User by ID (if found via mention, plain ID, or alias) ---
                if user_id_to_fetch:
                    try:
                        logger.debug(f"Attempting to fetch user by final ID for DM: {user_id_to_fetch}")
                        target_user = await bot.fetch_user(user_id_to_fetch) # Use bot.fetch_user for global lookup
                    except (discord.NotFound, discord.HTTPException) as e:
                        logger.warning(f"Failed to fetch user by final ID {user_id_to_fetch} for DM: {e}")
                        target_user = None # Ensure None on failure

                # --- Fallback: Try finding member by name/nickname in the current guild ---
                # This runs ONLY if no user was found via ID (mention, plain, or alias)
                if not target_user:
                    logger.debug(f"No user found via ID methods for '{log_identifier}'. Falling back to guild name/nickname search.")
                    # Use the same cleaned name as used for alias lookup (or re-clean if needed)
                    name_to_search_fallback = re.sub(r'[<@!>#()\[\]]', '', full_identifier_string).strip()
                    if name_to_search_fallback.lower().startswith("user "):
                        name_to_search_fallback = name_to_search_fallback[5:].strip()

                    if name_to_search_fallback: # Proceed only if name is not empty
                        log_identifier_fallback = f"Name/Nick '{name_to_search_fallback}' in Guild {message.guild.id}" # Update log identifier for fallback
                        try:
                            logger.debug(f"Attempting guild search for member by name/nick: '{name_to_search_fallback}'")
                            # Iterate through cached members (requires Members Intent)
                            found_in_guild = False
                            for member in message.guild.members:
                                if member.name.lower() == name_to_search_fallback.lower() or \
                                   (member.nick and member.nick.lower() == name_to_search_fallback.lower()):
                                    target_user = member # Found member object
                                    logger.info(f"Found potential DM target by name/nick in guild cache (fallback): {target_user.name} ({target_user.id})")
                                    log_identifier = log_identifier_fallback # Update final identifier used
                                    found_in_guild = True
                                    break
                            if not found_in_guild:
                                logger.warning(f"Fallback search: Member matching name/nick '{name_to_search_fallback}' not found in guild cache.")
                                target_user = None # Ensure target_user is None if not found by fallback
                        except Exception as e:
                            logger.error(f"Error during member iteration for DM fallback '{name_to_search_fallback}': {e}")
                            target_user = None
                    else:
                        logger.warning(f"Cleaned identifier '{full_identifier_string}' resulted in empty string for DM fallback name search.")
                        # log_identifier remains as it was before fallback attempt


                # --- Send DM ---
                if target_user: # Can be User or Member object now
                    try:
                        await target_user.send(message_content)
                        logger.info(f"Successfully sent DM to {target_user.name} ({target_user.id})")
                        await message.channel.send(f"✅ Sent DM to {target_user.mention}.")
                        brain_action_taken = True
                    except discord.Forbidden:
                        logger.warning(f"Cannot send DM to {target_user.name} ({target_user.id}) - Permissions/Privacy.")
                        await message.channel.send(f"⚠️ Couldn't send DM to {target_user.mention} (maybe they blocked me or have DMs disabled?).")
                        brain_action_taken = True # Still counts as action attempted
                    except discord.HTTPException as e:
                        logger.error(f"Failed to send DM to {target_user.name} ({target_user.id}): {e}")
                        await message.channel.send(f"❌ Failed to send DM to {target_user.mention} due to an API error.")
                        brain_action_taken = True # Still counts as action attempted
                else:
                    # If both ID and name lookup failed
                    logger.warning(f"Could not find user matching '{log_identifier}' to DM after checking ID and guild name.")
                    await message.channel.send(f"❓ Couldn't find user matching '{log_identifier}' to DM.")
                    brain_action_taken = True # Mark action attempted

            # Example: Call Action (Placeholder)
            call_match = re.search(r"call\s+(.*)", decision_lower)
            if call_match and not brain_action_taken:
                full_identifier_string = call_match.group(1).strip()
                logger.info(f"Brain decided to call user based on: '{full_identifier_string}'")
                # Placeholder: Implement user lookup (similar to DM/disconnect)
                # Placeholder: Implement call initiation logic (e.g., move to channel, start call API?)
                log_identifier = full_identifier_string # Placeholder
                await message.channel.send(f"🚧 Brain wants to call '{log_identifier}', but the call action isn't implemented yet.")
                logger.warning(f"Call action for '{log_identifier}' is not implemented.")
                brain_action_taken = True # Mark action attempted

            # Add more action parsing here if needed later

        # This except block handles errors during brain processing/action execution
        except Exception as brain_err:
            logger.error(f"Error during LLM Brain processing or action execution for message ID {message.id}: {brain_err}", exc_info=True)
            # Optional: Notify user about brain error
            # await message.channel.send("🧠 My decision circuits had an error.")
            # Decide if we should still attempt a default response. Let's allow it for now.
            # brain_action_taken = False # Explicitly allow fallback

        # --- If Brain Didn't Take Action (or failed and fallback is allowed), Proceed with Regular Response ---
        if not brain_action_taken:
            logger.info(f"LLM Brain took no specific action (or failed), proceeding with conversational response for message ID {message.id}.")

            # --- Get or Create Chat Session for Text Channel ---
            channel_id = message.channel.id
            session_key = f"text_{channel_id}"

            # Try to get session from bot.chat_sessions first (global access)
            session = bot.chat_sessions.get(session_key)

            # If not found in bot.chat_sessions, check text_chat_sessions (legacy)
            if session is None:
                session = text_chat_sessions.get(channel_id)
                # If found in legacy storage, migrate it to bot.chat_sessions
                if session is not None:
                    logger.info(f"Migrating session from text_chat_sessions to bot.chat_sessions for channel {channel_id}")
                    bot.chat_sessions[session_key] = session

            lm_studio_client = llm_response.get_lm_studio_client() # Use getter function

            if session is None and lm_studio_client:
                logger.info(f"Creating new Ollama Chat Session for text channel {channel_id}.")
                try:
                    # Create a session ID for this channel
                    session_id = f"channel_{channel_id}_{int(time.time())}"

                    # Store the session ID and initialize message history with system prompt
                    session = {
                        "id": session_id,
                        "messages": [{"role": "system", "content": SYSTEM_PROMPT}]
                    }

                    # Log session creation
                    logger.debug(f"Created new session with ID: {session_id}")

                    # Store in both dictionaries for compatibility
                    text_chat_sessions[channel_id] = session
                    bot.chat_sessions[session_key] = session

                    logger.info(f"Successfully created session for channel {channel_id}. Session has {len(session['messages'])} messages.")
                except Exception as e:
                    logger.error(f"Failed to create chat session for channel {channel_id}: {e}", exc_info=True)
                    session = None # Ensure session is None if creation failed
            elif not lm_studio_client:
                 logger.error(f"Cannot create/use session for channel {channel_id}: LM Studio client not initialized.")
                 session = None
            else:
                 # Session exists, log its current state
                 logger.info(f"Using existing chat session for channel {channel_id}. Session has {len(session['messages'])} messages.")

                 # Log the first few messages in the session for debugging
                 if len(session["messages"]) > 0:
                     logger.info(f"First message in session: role={session['messages'][0].get('role')}, content={session['messages'][0].get('content')[:50]}...")
                 if len(session["messages"]) > 1:
                     logger.info(f"Second message in session: role={session['messages'][1].get('role')}, content={session['messages'][1].get('content')[:50]}...")

            # --- Call llm_response.process_user_message ---
            try:
                await llm_response.process_user_message(
                    bot=bot,
                    text=message.content,
                    user_id=message.author.id,
                    conversation_history=None, # History is managed by session or stateless call
                    system_prompt=SYSTEM_PROMPT, # Pass for stateless fallback if session is None
                    sink=None, # No sink for text messages
                    force_respond=True, # Respond since brain didn't act
                    display_name=message.author.display_name,
                    text_channel=message.channel,
                    image_analysis_text=image_analysis_result, # Pass analysis if available
                    chat_session=session, # Pass the retrieved/created session
                    brain_decision_context=brain_decision # Pass the brain's decision context
                )
            except Exception as llm_err:
                logger.error(f"Error calling process_user_message for text message {message.id}: {llm_err}", exc_info=True)
                await message.channel.send("Sorry, I had trouble processing that.")
            # Removed stray lines after except block

@bot.event
async def on_close():
    """Clean up resources when bot is shutting down"""
    logger.info("Bot shutting down, cleaning up resources...")
    await close_tts_engine()

# Initialize memory components
async def initialize_memory_systems():
    logger.info("Initializing enhanced memory systems...")

    # Initialize RAG retriever
    rag_retriever_instance = None
    enhanced_memory_instance = None
    memory_integration_instance = None

    try:
        # Initialize Enhanced Memory System
        from enhanced_memory_system import EnhancedMemorySystem
        enhanced_memory_instance = EnhancedMemorySystem(db_path="enhanced_memories.db")
        logger.info("Enhanced memory system initialized successfully")

        # Initialize RAG retriever
        rag_retriever_instance = RAGRetriever()

        # Perform an initial index build/check if needed
        if not rag_retriever_instance.is_ready and rag_retriever_instance.needs_rebuild():
            logger.info("Performing initial RAG index build...")
            build_start_time = time.monotonic()
            await asyncio.to_thread(rag_retriever_instance.build_index)
            build_duration = time.monotonic() - build_start_time
            if rag_retriever_instance.is_ready:
                 logger.info(f"Initial RAG index build successful in {build_duration:.2f}s.")
            else:
                 logger.error("Initial RAG index build attempt finished, but retriever is still not ready.")
        elif rag_retriever_instance.is_ready:
             logger.info("RAG index loaded and is up-to-date.")
        else:
             logger.info("RAG retriever initialized, but no rebuild needed (index might be empty).")

        # Initialize Memory Integration (combines enhanced memory + RAG)
        from memory_integration import LunaMemoryIntegration
        memory_integration_instance = LunaMemoryIntegration(
            enhanced_memory=enhanced_memory_instance,
            existing_rag_retriever=rag_retriever_instance
        )
        logger.info("Memory integration system initialized successfully")

    except Exception as init_err:
        logger.error(f"Failed to initialize memory systems during startup: {init_err}", exc_info=True)
        rag_retriever_instance = None
        enhanced_memory_instance = None
        memory_integration_instance = None

    # Store all memory components on the bot for access
    bot.rag_retriever = rag_retriever_instance
    bot.enhanced_memory = enhanced_memory_instance
    bot.memory_integration = memory_integration_instance

    logger.info("Enhanced memory systems initialization complete")
    return rag_retriever_instance  # Return RAG for backward compatibility

if __name__ == "__main__":
    print(">>> Starting main execution block <<<") # Added print
    try: # Added try block
        # Warm up CUDA first
        print(">>> Calling warmup_cuda <<<") # Added print
        warmup_cuda()
        print(">>> Finished warmup_cuda <<<") # Added print
        # Then warm up the transcription model
        print(">>> Calling warmup_transcription_model <<<") # Added print
        warmup_transcription_model()
        print(">>> Finished warmup_transcription_model <<<") # Added print
        # Run the bot
        print(">>> Calling bot.run <<<") # Added print
        bot.run(DISCORD_BOT_TOKEN)
        print(">>> bot.run finished <<<") # Added print (might not be reached if bot runs forever)
    except Exception as main_err: # Added except block
        print(f"!!! ERROR in main execution block: {main_err} !!!") # Added print
        logger.exception("Error during main execution:") # Log the full traceback
    finally: # Added finally block
        print(">>> Exiting main execution block <<<") # Added print
