import os
import logging
import asyncio
import io
import re
import discord
import time
import torch
import threading
import traceback

# Import RealtimeTTS and KokoroEngine
import RealtimeTTS
from RealtimeTTS.engines import KokoroEngine

# Preprocess text to reduce pauses at punctuation
def preprocess_text_for_tts(text):
    """Intelligently process punctuation to reduce long pauses while maintaining natural speech"""
    import re

    # First, preserve the original text for logging
    original_text = text

    # Replace multiple periods/exclamation/question marks with a single one
    text = re.sub(r'\.{2,}', '.', text)
    text = re.sub(r'\!{2,}', '!', text)
    text = re.sub(r'\?{2,}', '?', text)

    # Add spaces after punctuation if missing to improve pronunciation
    text = re.sub(r'([.!?,;:])([^\s])', r'\1 \2', text)

    # Add slight pauses between words that might run together
    # For example: "pouch potato" -> "pouch, potato"
    text = re.sub(r'(\w+)\s+(potato|kangaroo|alright)', r'\1, \2', text)

    # For sentences in the middle of the text, replace periods with commas
    # But keep the final period to maintain natural speech cadence
    sentences = re.split(r'([.!?])', text)
    if len(sentences) > 2:  # If we have multiple sentences
        # Keep the last sentence intact
        last_sentence = sentences[-2] + sentences[-1] if len(sentences) % 2 == 0 else sentences[-1]
        # Process middle sentences
        middle_text = ''.join(sentences[:-2]) if len(sentences) % 2 == 0 else ''.join(sentences[:-1])
        middle_text = re.sub(r'\. ', ', ', middle_text)  # Replace period+space with comma+space
        middle_text = re.sub(r'! ', ', ', middle_text)   # Replace exclamation+space with comma+space
        middle_text = re.sub(r'\? ', ', ', middle_text)  # Replace question+space with comma+space
        text = middle_text + last_sentence

    # Replace multiple commas with a single comma
    text = re.sub(r',+', ',', text)

    # Replace comma+space+comma with just comma+space
    text = re.sub(r', ,', ', ', text)

    # Add emphasis to important words by adding a comma before them
    text = re.sub(r'\b(call|lazy|pouch|potato)\b', r', \1', text)

    # Log the transformation for debugging
    if original_text != text:
        logger.debug(f"Transformed text for TTS:\nOriginal: {original_text}\nModified: {text}")

    return text

logger = logging.getLogger(__name__)

# Configuration for Kokoro TTS
# Use the mixed voice format as in terminal_tts.py
INITIAL_VOICE = "af_nicole"  # Base voice for initialization
MIXED_VOICE_STRING = os.getenv("KOKORO_VOICE", "2*af_nicole + 5*zf_xiaoyi").strip('"\'')
KOKORO_FALLBACK_VOICE = INITIAL_VOICE  # Use the same base voice as fallback

# Global engine instance
engine = None
engine_lock = threading.Lock()

# Initialize the KokoroEngine in a separate thread to avoid blocking
def initialize_engine():
    global engine
    try:
        logger.info(f"Initializing KokoroEngine with initial voice: {INITIAL_VOICE}...")

        # Check CUDA availability
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_device)
            logger.info(f"CUDA is available! Found {gpu_count} GPU(s).")
            logger.info(f"Current CUDA device: {current_device} - {gpu_name}")
        else:
            logger.info("CUDA is NOT available. PyTorch will run on CPU.")

        # Initialize with initial voice first and slightly faster speed
        with engine_lock:
            # Use a slightly faster speed (1.2) to help reduce the perception of pauses
            engine = KokoroEngine(default_voice=INITIAL_VOICE, default_speed=1.2, debug=False)
            logger.info(f"KokoroEngine initialized with slightly faster speed to reduce pauses.")

            # Set the desired mixed voice
            logger.info(f"Setting voice to custom mix: {MIXED_VOICE_STRING}")
            try:
                # Try to set the mixed voice
                engine.set_voice(MIXED_VOICE_STRING)
                logger.info(f"Engine voice set.")
            except Exception as voice_error:
                logger.warning(f"Error setting mixed voice: {voice_error}")
                logger.warning(f"Falling back to default voice: {INITIAL_VOICE}")

            # Pre-warm the engine
            logger.info("Pre-warming engine...")
            try:
                prewarm_stream = RealtimeTTS.TextToAudioStream(engine)
                prewarm_stream.feed("Engine warm-up.").play(muted=True)
                time.sleep(0.5)  # Allow time for pre-warming
                prewarm_stream.stop()
                logger.info("Engine pre-warmed.")
            except Exception as e_warm:
                logger.warning(f"Pre-warming failed: {e_warm}")

        logger.info(f"TTS Initialized using KokoroEngine (Mix: {MIXED_VOICE_STRING}).")
        return True
    except Exception as e:
        logger.error(f"Error initializing KokoroEngine: {e}")
        logger.error(traceback.format_exc())
        return False

# Start initialization in a separate thread
engine_init_thread = threading.Thread(target=initialize_engine)
engine_init_thread.daemon = True
engine_init_thread.start()

async def synthesize_speech(text):
    """Synthesizes speech using RealtimeTTS with KokoroEngine.
    Returns audio bytes that can be played by discord.py.
    """
    global engine

    try:
        # Wait for engine initialization if needed
        if engine is None:
            logger.info("Waiting for KokoroEngine to initialize...")
            for _ in range(30):  # Wait up to 30 seconds
                if engine is not None:
                    break
                await asyncio.sleep(0.5)

            if engine is None:
                logger.error("KokoroEngine initialization timed out")
                raise Exception("TTS engine not initialized")

        # Preprocess the text to reduce pauses at punctuation
        processed_text = preprocess_text_for_tts(text)
        logger.info(f"Original text: '{text[:30]}...'")
        logger.info(f"Processed text: '{processed_text[:30]}...'")

        logger.info(f"Synthesizing speech: '{processed_text[:30]}...'")
        t_start = time.monotonic()

        # Create a buffer to store the audio data
        audio_buffer = io.BytesIO()

        # Run the synthesis in a thread to avoid blocking the event loop
        # Pass the main event loop to the thread function
        def _synthesize_to_buffer(main_loop):
            try: # Main try block for the thread function
                thread_start_time = time.monotonic()
                logger.debug(f"⏱️ [TTS-TIMING] Starting synthesis thread")

                with engine_lock:  # Ensure thread safety when accessing the engine
                    engine_lock_acquired_time = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Engine lock acquisition took {engine_lock_acquired_time - thread_start_time:.4f}s")

                    # Create a stream with the engine
                    stream_creation_start = time.monotonic()
                    stream = RealtimeTTS.TextToAudioStream(engine)
                    stream_creation_end = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Stream creation took {stream_creation_end - stream_creation_start:.4f}s")

                    # File-based synthesis logic moved inside the 'with engine_lock'
                    logger.info("Using file-based synthesis approach.")
                    import tempfile
                    import os

                    temp_path = None # Define outside the 'with' block for cleanup
                    try: # Try block specifically for file operations
                        # Create a temporary file to save the audio
                        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                            temp_path = temp_file.name
                            logger.debug(f"⏱️ [TTS-TIMING] Created temp file: {temp_path}")

                        # Feed the processed text to the stream (needs a list)
                        feed_start_time = time.monotonic()
                        stream_obj = stream.feed([processed_text])
                        feed_end_time = time.monotonic()
                        logger.debug(f"⏱️ [TTS-TIMING] Stream feed took {feed_end_time - feed_start_time:.4f}s")

                        # Play to the file - this call should block until file is written
                        play_start_time = time.monotonic()
                        logger.info(f"Playing text to temp file: '{processed_text[:30]}...'")
                        stream_obj.play(
                            output_wavfile=temp_path,
                            muted=True,
                            tokenize_sentences=False,
                            buffer_threshold_seconds=0.5,
                            minimum_sentence_length=10000,
                            fast_sentence_fragment=False
                        )
                        play_end_time = time.monotonic()
                        # Log the duration, assuming play() blocked correctly
                        logger.debug(f"⏱️ [TTS-TIMING] stream.play() call (blocking) took {play_end_time - play_start_time:.4f}s")

                        # Read the completed file into the buffer
                        file_read_start = time.monotonic()
                        with open(temp_path, 'rb') as f:
                            audio_data = f.read()
                            logger.info(f"Read audio file size: {len(audio_data)} bytes")
                            audio_buffer.write(audio_data)
                            audio_buffer.seek(0)
                        file_read_end = time.monotonic()
                        logger.debug(f"⏱️ [TTS-TIMING] File read took {file_read_end - file_read_start:.4f}s")

                    finally: # Finally block for file operations (delete temp file)
                        # Ensure temporary file is deleted even if errors occur
                        if temp_path and os.path.exists(temp_path):
                            try:
                                os.unlink(temp_path)
                                logger.debug(f"⏱️ [TTS-TIMING] Deleted temp file: {temp_path}")
                            except Exception as e_unlink:
                                logger.warning(f"Could not delete temporary file '{temp_path}': {e_unlink}")

                    # Clean up stream object (still inside 'with engine_lock')
                    cleanup_start_time = time.monotonic()
                    stream.stop() # Ensure stream resources are released
                    cleanup_end_time = time.monotonic()
                    logger.debug(f"⏱️ [TTS-TIMING] Stream cleanup took {cleanup_end_time - cleanup_start_time:.4f}s")

                # Log total thread time (outside 'with engine_lock', but inside main try)
                thread_end_time = time.monotonic()
                logger.debug(f"⏱️ [TTS-TIMING] Total synthesis thread time: {thread_end_time - thread_start_time:.4f}s")

            except Exception as e: # Except block for the main thread function try
                logger.error(f"Error in _synthesize_to_buffer: {e}")
                logger.error(traceback.format_exc())
        # --- End of _synthesize_to_buffer definition ---

        # --- Code back in synthesize_speech ---
        # Get the main event loop
        main_loop = asyncio.get_running_loop()
        # Run the synthesis in a thread, passing the main loop
        await asyncio.to_thread(_synthesize_to_buffer, main_loop)

        # Get the audio data from the buffer
        audio_data = audio_buffer.getvalue()

        t_end = time.monotonic()
        logger.info(f"⏱️ [TTS-TOTAL] Complete in {t_end - t_start:.4f}s")

        return audio_data # Return from synthesize_speech
    except Exception as e:
        logger.error(f"Error in synthesize_speech: {e}")
        logger.error(traceback.format_exc())
        raise Exception(f"Speech synthesis failed: {e}")

class StreamingSpeechPlayer:
    def __init__(self, voice_client, after_play_callback=None):
        self.voice_client = voice_client
        self.after_play_callback = after_play_callback
        self.queue = asyncio.Queue()
        self.current_audio = None
        self.running = False
        self.task = None
        self.buffer = ""
        # Modified sentence endings pattern to be more selective
        # Only split on periods followed by spaces and capital letters (likely new sentences)
        # or on question/exclamation marks followed by spaces
        self.sentence_endings = re.compile(r'(?<=[.])\s+(?=[A-Z])|(?<=[!?])\s+')
        self.min_chunk_len = 30  # Minimum characters before processing
        self.first_chunk_sent = False  # Flag for immediate first chunk
        self.synthesis_task = None  # Task for prefetching next synthesis
        self.realtime_stream = None  # RealtimeTTS stream for direct playback
        # Removed self.playback_finished event, will use Future per-playback

    async def start(self):
        """Start the streaming player task"""
        if self.running:
            return
        self.running = True
        self.first_chunk_sent = False  # Reset flag when starting
        self.task = asyncio.create_task(self._player_loop())

    async def stop(self):
        """Stop the streaming player task"""
        self.running = False

        # Cancel the prefetch task if it exists
        if self.synthesis_task and not self.synthesis_task.done():
            self.synthesis_task.cancel()
            try:
                await self.synthesis_task
            except asyncio.CancelledError:
                pass
            self.synthesis_task = None

        if self.task:
            await self.task
            self.task = None

        # Clear any remaining items in the queue
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
                self.queue.task_done()
            except asyncio.QueueEmpty:
                break

        # Stop any currently playing audio
        if self.voice_client and self.voice_client.is_playing():
            self.voice_client.stop()

    async def add_text(self, text):
        """Add text to the streaming buffer with a larger first chunk for smoother transitions"""
        if not text:
            return

        self.buffer += text

        # Immediately send first chunk if we haven't sent anything yet,
        # but make it much larger to reduce gaps between first and subsequent chunks
        if not self.first_chunk_sent and self.buffer:
            # For the first chunk, we want enough text for a substantial utterance
            words = self.buffer.split()

            # Require at least 8 words or 40+ characters for a substantial first chunk
            if len(words) >= 8 or (len(self.buffer) >= 40 and ' ' in self.buffer):
                # Try to find a natural break point - preferably at a sentence end
                # or after several words
                # Only split on periods followed by spaces and capital letters (likely new sentences)
                # or on question/exclamation marks followed by spaces
                sentence_end = re.search(r'(?<=[.])\s+(?=[A-Z])|(?<=[!?])\s+', self.buffer)
                if sentence_end and sentence_end.end() >= 40:
                    # If there's a sentence end after at least 40 chars, break there
                    chunk_to_send = self.buffer[:sentence_end.end()]
                    self.buffer = self.buffer[sentence_end.end():]
                elif len(words) >= 8:
                    # If we have at least 8 words, send a larger chunk
                    # Try to find a good break point after the 8th word
                    words_to_include = min(12, len(words))  # Include up to 12 words
                    space_after_words = self.buffer.find(' ', len(' '.join(words[:words_to_include])))
                    if space_after_words > 0:
                        chunk_to_send = self.buffer[:space_after_words]
                        self.buffer = self.buffer[space_after_words:]
                    else:
                        # Otherwise send everything
                        chunk_to_send = self.buffer
                        self.buffer = ""
                else:
                    # If we don't have 8 words but have enough text, send what we have
                    chunk_to_send = self.buffer
                    self.buffer = ""

                self.first_chunk_sent = True
                logger.info(f"Sending larger initial chunk: '{chunk_to_send[:50]}...' ({len(chunk_to_send)} chars, ~{len(chunk_to_send.split())} words)")
                await self._synthesize_and_queue(chunk_to_send)
            else:
                # Not enough text for a substantial first chunk yet, continue accumulating
                pass
        else:
            # Process buffer using normal logic for subsequent text
            await self._process_buffer()

    async def finalize(self):
        """Process any remaining text in buffer"""
        if self.buffer:
            await self._synthesize_and_queue(self.buffer)
            self.buffer = ""

        # Add a special sentinel to signal we're done
        await self.queue.put(None)

    async def _process_buffer(self):
        """Process buffered text for subsequent chunks, respecting word boundaries"""
        # Skip if this is the first chunk (handled in add_text)
        if not self.first_chunk_sent:
            return

        processed_chunk = False  # Flag to track if we processed a chunk

        # If buffer has reached minimum length, try to find sentence breaks
        if len(self.buffer) >= self.min_chunk_len:
            splits = list(self.sentence_endings.finditer(self.buffer))
            if splits:
                # Get the position of the last sentence ending
                last_split = splits[-1].end()
                chunk = self.buffer[:last_split]
                self.buffer = self.buffer[last_split:]
                # Synthesize this complete sentence
                await self._synthesize_and_queue(chunk)
                processed_chunk = True
            elif len(self.buffer) > 100:  # Hard limit to avoid very long waits
                # No sentence break but buffer is getting too long
                # Find the last word boundary to avoid cutting words in the middle
                last_space = self.buffer.rfind(' ')
                if last_space > self.min_chunk_len:  # Only use word boundary if it gives a reasonable chunk
                    chunk = self.buffer[:last_space+1]  # Include the space
                    self.buffer = self.buffer[last_space+1:]
                else:
                    # If no good word boundary, use the whole buffer
                    chunk = self.buffer
                    self.buffer = ""
                await self._synthesize_and_queue(chunk)
                processed_chunk = True

        # Return whether a chunk was processed and queued
        return processed_chunk

    async def _synthesize_and_queue(self, text):
        """Synthesize a chunk of text and add to queue"""
        try:
            # Log synthesis request with timing
            logger.info(f"Synthesizing text chunk: '{text[:30]}...' ({len(text)} chars)")
            t_start_tts_synth = time.monotonic()

            # Get audio data from the synthesize_speech function
            audio_data = await synthesize_speech(text)

            t_end_tts_synth = time.monotonic()
            tts_synth_duration = t_end_tts_synth - t_start_tts_synth
            logger.info(f"⏱️ [TTS-SYNTH] Synthesis completed in {tts_synth_duration:.4f}s for {len(text)} chars")

            if audio_data:  # Only queue if synthesis was successful
                await self.queue.put(audio_data)

                # Start prefetching the next chunk if there's more in the buffer
                # and we don't already have a synthesis task running
                if self.buffer and self.first_chunk_sent and (not self.synthesis_task or self.synthesis_task.done()):
                    # Check if we can extract the next chunk
                    next_chunk = None
                    splits = list(self.sentence_endings.finditer(self.buffer))
                    if splits:
                        last_split = splits[-1].end()
                        next_chunk = self.buffer[:last_split]
                    elif len(self.buffer) > self.min_chunk_len:
                        next_chunk = self.buffer

                    if next_chunk:
                        # Prefetch the next chunk, but don't modify the buffer yet
                        self.synthesis_task = asyncio.create_task(self._prefetch_next_chunk(next_chunk))
            else:
                logger.warning("TTS synthesis returned no data, skipping queue.")
        except Exception as e:
            logger.error(f"Error during speech synthesis: {e}")
            logger.error(traceback.format_exc())

    async def _monitor_playback(self, segment_index):
        """Monitor audio playback without blocking the main processing loop"""
        try:
            # Log the start of monitoring
            logger.debug(f"Started monitoring playback for segment {segment_index}")

            # Wait for the audio to finish playing or for the player to stop
            while self.voice_client.is_playing() and self.running:
                await asyncio.sleep(0.02)  # Short sleep to avoid CPU hogging

            # Log completion of playback
            if self.running:
                logger.debug(f"Playback completed for segment {segment_index}")
            else:
                logger.debug(f"Monitoring stopped for segment {segment_index} (player not running)")

        except Exception as e:
            logger.error(f"Error monitoring playback for segment {segment_index}: {e}")

    async def _prefetch_next_chunk(self, text):
        """Prefetch synthesis for the next chunk while current audio is playing"""
        try:
            logger.info(f"Prefetching next chunk: '{text[:30]}...' ({len(text)} chars)")

            # Use the async version directly
            prefetched_audio = await synthesize_speech(text)

            # Store the prefetched result in a special queue entry
            if prefetched_audio:
                await self.queue.put(("prefetched", text, prefetched_audio))

        except Exception as e:
            logger.error(f"Error prefetching synthesis: {e}")
            # We don't propagate the error since this is just an optimization

    async def _player_loop(self):
        """Main player loop that processes the audio queue"""
        segment_index = 0

        while self.running:
            try:
                # Wait for the next audio chunk
                queue_item = await self.queue.get()

                # Handle prefetched data
                if isinstance(queue_item, tuple) and queue_item[0] == "prefetched":
                    _, prefetched_text, _ = queue_item  # We don't need the audio data anymore
                    logger.info(f"Received prefetched audio for '{prefetched_text[:30]}...'")

                    # Remove the corresponding text from buffer if it's still there
                    if self.buffer.startswith(prefetched_text):
                        self.buffer = self.buffer[len(prefetched_text):]

                    # Use the prefetched audio directly
                    self.queue.task_done()
                    continue

                # None is our sentinel value to indicate we're done
                if queue_item is None:
                    logger.info(f"Player loop finished")
                    self.queue.task_done()
                    if self.after_play_callback:
                        # Use call_soon_threadsafe if callback interacts with discord.py
                        asyncio.get_event_loop().call_soon_threadsafe(self.after_play_callback, None)
                    break

                # Use regular audio data
                audio_data = queue_item

                segment_index += 1
                logger.info(f"Starting playback for segment {segment_index}")

                # Create a buffer and play the audio
                buffer_start_time = time.monotonic()
                buffer = io.BytesIO(audio_data)
                buffer.seek(0)
                buffer_end_time = time.monotonic()
                logger.debug(f"⏱️ [DISCORD-TIMING] Buffer creation took {buffer_end_time - buffer_start_time:.4f}s")

                # --- Start: Use asyncio.Event for reliable sequencing ---
                if not self.running:
                    logger.info("Player loop: Not running, breaking before playback.")
                    self.queue.task_done() # Mark task done before breaking
                    break

                # Create audio source using FFmpegOpusAudio
                ffmpeg_start_time = time.monotonic()
                source = discord.FFmpegOpusAudio(
                    buffer,
                    pipe=True,
                    bitrate=96,
                    options='-loglevel warning -af aresample=async=1000'
                )
                ffmpeg_end_time = time.monotonic()
                logger.debug(f"⏱️ [DISCORD-TIMING] FFmpegOpusAudio creation took {ffmpeg_end_time - ffmpeg_start_time:.4f}s")

                # Clear the event before starting playback for this segment
                # Create a future in the correct loop to signal playback completion
                loop = self.voice_client.loop
                playback_done_future = loop.create_future()

                # Define the after callback function to set the future's result
                def after_callback(error):
                    # This runs in discord.py's thread
                    if error:
                        logger.error(f"Playback error on segment {segment_index}: {error}")
                        # Set future result safely from the other thread
                        loop.call_soon_threadsafe(playback_done_future.set_result, False) # Indicate error
                    else:
                        logger.debug(f"Playback finished callback received for segment {segment_index}")
                        # Set future result safely from the other thread
                        loop.call_soon_threadsafe(playback_done_future.set_result, True) # Indicate success

                    # Call the external callback if provided (using threadsafe call)
                    # Pass the error argument to the external callback
                    if self.after_play_callback:
                        # Ensure this callback is also scheduled safely if it interacts with asyncio/discord.py
                        loop.call_soon_threadsafe(self.after_play_callback, error) # Pass error directly

                # Play the audio
                play_start_time = time.monotonic()
                self.voice_client.play(source, after=after_callback)
                play_end_time = time.monotonic()
                logger.debug(f"⏱️ [DISCORD-TIMING] Voice client play() call took {play_end_time - play_start_time:.4f}s")

                # Start prefetching next chunk while this one plays (if applicable)
                if self.buffer and (not self.synthesis_task or self.synthesis_task.done()):
                    next_chunk = None
                    splits = list(self.sentence_endings.finditer(self.buffer))
                    if splits:
                        last_split = splits[-1].end()
                        next_chunk = self.buffer[:last_split]
                    elif len(self.buffer) > self.min_chunk_len:
                        next_chunk = self.buffer
                    if next_chunk:
                         logger.debug(f"Starting prefetch task for next chunk while segment {segment_index} plays.")
                         self.synthesis_task = asyncio.create_task(self._prefetch_next_chunk(next_chunk))

                # Wait for the future to be set by the after_callback
                logger.debug(f"Waiting for playback completion future for segment {segment_index}...")
                try:
                    success = await playback_done_future
                    if not success:
                         logger.warning(f"Playback for segment {segment_index} reported an error.")
                    # Future completed, proceed to next segment
                except asyncio.CancelledError:
                    logger.warning(f"Playback future for segment {segment_index} cancelled.")
                    break # Exit loop if cancelled
                logger.info(f"Playback completed for segment {segment_index}. Proceeding to next item.")
                # --- End: Use asyncio.Event for reliable sequencing ---

                self.queue.task_done()

            except Exception as e:
                logger.error(f"Error in player loop: {e}", exc_info=True)
                # Ensure task_done is called even on error to prevent deadlocks
                try:
                    self.queue.task_done()
                except ValueError:  # If task_done() already called or queue empty
                    pass

# Make sure to add this to main.py to clean up the engine on shutdown
async def close_tts_engine():
    """Shut down the TTS engine when the bot is shutting down"""
    global engine
    try:
        with engine_lock:
            if engine:
                logger.info("Shutting down KokoroEngine...")
                engine.shutdown()
                engine = None
                logger.info("KokoroEngine shut down successfully")
    except Exception as e:
        logger.error(f"Error shutting down KokoroEngine: {e}")
        logger.error(traceback.format_exc())