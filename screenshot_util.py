import os
import io
import base64
import logging
import asyncio
from datetime import datetime
from PIL import ImageGrab

logger = logging.getLogger(__name__)

class ScreenshotManager:
    """Manages screenshot capture and preparation for Gemini multimodal analysis"""
    
    def __init__(self):
        # Create screenshots directory if it doesn't exist
        os.makedirs('screenshots', exist_ok=True)
        
    async def capture_screenshot(self):
        """Capture a full screen screenshot and return relevant data"""
        try:
            # Use PIL to capture the screen
            screenshot = await asyncio.to_thread(ImageGrab.grab)
            
            # Generate a filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshots/screenshot_{timestamp}.png"
            
            # Save the screenshot to disk
            await asyncio.to_thread(screenshot.save, filename)
            logger.info(f"Screenshot saved to {filename}")
            
            # Convert to bytes for API submission
            img_bytes = io.BytesIO()
            screenshot.save(img_bytes, format='PNG')
            img_bytes.seek(0)
            
            return {
                "success": True,
                "filename": filename,
                "bytes": img_bytes.getvalue(),
                "timestamp": timestamp,
                "width": screenshot.width,
                "height": screenshot.height
            }
        except Exception as e:
            logger.error(f"Error capturing screenshot: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def encode_image_base64(self, image_bytes):
        """Convert image bytes to base64 encoding for API submission"""
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def get_mime_type(self, filename):
        """Get MIME type based on file extension"""
        ext = os.path.splitext(filename)[1].lower()
        mime_types = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg', 
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp'
        }
        return mime_types.get(ext, 'application/octet-stream')

# Create singleton instance
screenshot_manager = ScreenshotManager() 