"""
Enhanced Multi-Tier Memory System for Luna
Based on cognitive science research for conversational AI consistency
"""

import asyncio
import sqlite3
import time
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class Memory:
    """Represents a single memory with metadata"""
    id: str
    content: str
    memory_type: str  # 'core_trait', 'preference', 'fact', 'interaction'
    importance_score: float  # 0.0 to 1.0
    recency_score: float    # 0.0 to 1.0
    access_count: int
    last_accessed: datetime
    created_at: datetime
    user_id: str
    context: Dict[str, Any]
    consolidated: bool = False

class MemoryTier:
    """Represents different tiers of memory storage"""
    CORE_PERSONALITY = "core_personality"    # Luna's fundamental traits
    LONG_TERM_FACTS = "long_term_facts"     # Important established facts
    PREFERENCES = "preferences"              # User/Luna preferences
    RECENT_INTERACTIONS = "recent"          # Recent conversation context
    WORKING_MEMORY = "working"              # Current conversation

class EnhancedMemorySystem:
    """
    Multi-tier memory system with consolidation and importance scoring
    """

    def __init__(self, db_path: str, embedding_function=None):
        self.db_path = db_path
        self.embedding_function = embedding_function
        self.memory_cache = {}  # In-memory cache for frequently accessed memories
        self.consolidation_threshold = 5  # Consolidate after 5 conflicting memories

        # Initialize database
        self._init_database()

        # Load core personality traits
        self._load_core_personality()

    def _init_database(self):
        """Initialize enhanced memory database schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Enhanced memory table with importance and consolidation tracking
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS enhanced_memories (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                memory_type TEXT NOT NULL,
                importance_score REAL NOT NULL,
                recency_score REAL NOT NULL,
                access_count INTEGER DEFAULT 0,
                last_accessed TIMESTAMP,
                created_at TIMESTAMP NOT NULL,
                user_id TEXT,
                context TEXT,  -- JSON
                consolidated BOOLEAN DEFAULT FALSE,
                superseded_by TEXT,  -- ID of memory that superseded this one
                embedding BLOB  -- Vector embedding if available
            )
        """)

        # Memory conflicts table for tracking contradictions
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS memory_conflicts (
                id TEXT PRIMARY KEY,
                topic TEXT NOT NULL,
                conflicting_memories TEXT,  -- JSON array of memory IDs
                resolution_memory_id TEXT,
                resolved_at TIMESTAMP,
                confidence_score REAL
            )
        """)

        # Memory consolidation log
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS consolidation_log (
                id TEXT PRIMARY KEY,
                consolidation_type TEXT,
                input_memories TEXT,  -- JSON array
                output_memory_id TEXT,
                performed_at TIMESTAMP,
                reasoning TEXT
            )
        """)

        conn.commit()
        conn.close()

    def _load_core_personality(self):
        """Load Luna's core personality traits that should remain consistent"""
        core_traits = [
            {
                "content": "Luna speaks in lowercase and uses casual language like 'u' and 'ur'",
                "importance": 1.0,
                "trait_type": "communication_style"
            },
            {
                "content": "Luna is witty, sassy, and playful in conversations",
                "importance": 1.0,
                "trait_type": "personality"
            },
            {
                "content": "Luna loves gaming, especially Among Us and Minecraft",
                "importance": 0.9,
                "trait_type": "interests"
            },
            {
                "content": "Luna uses occasional mild profanity but not excessively",
                "importance": 0.8,
                "trait_type": "communication_style"
            },
            {
                "content": "Gavin is Luna's creator",
                "importance": 1.0,
                "trait_type": "relationships"
            }
        ]

        # Store core traits if not already present
        for trait in core_traits:
            self.store_memory(
                content=trait["content"],
                memory_type=MemoryTier.CORE_PERSONALITY,
                importance_score=trait["importance"],
                user_id="system",
                context={"trait_type": trait["trait_type"]}
            )

    def store_memory(self, content: str, memory_type: str, importance_score: float = 0.5,
                    user_id: str = None, context: Dict = None) -> str:
        """Store a new memory with automatic importance scoring"""

        memory_id = str(uuid.uuid4())
        now = datetime.now()

        # Auto-calculate importance if not provided
        if importance_score == 0.5:  # Default value
            importance_score = self._calculate_importance(content, memory_type, context)

        # Create memory object
        memory = Memory(
            id=memory_id,
            content=content,
            memory_type=memory_type,
            importance_score=importance_score,
            recency_score=1.0,  # New memories start with max recency
            access_count=0,
            last_accessed=now,
            created_at=now,
            user_id=user_id,
            context=context or {},
            consolidated=False
        )

        # Store in database
        self._store_memory_db(memory)

        # Check for conflicts with existing memories
        self._check_for_conflicts(memory)

        return memory_id

    def _calculate_importance(self, content: str, memory_type: str, context: Dict = None) -> float:
        """Calculate importance score based on content and context"""

        # Base importance by memory type
        type_importance = {
            MemoryTier.CORE_PERSONALITY: 1.0,
            MemoryTier.LONG_TERM_FACTS: 0.8,
            MemoryTier.PREFERENCES: 0.7,
            MemoryTier.RECENT_INTERACTIONS: 0.3,
            MemoryTier.WORKING_MEMORY: 0.1
        }

        base_score = type_importance.get(memory_type, 0.5)

        # Boost importance for certain keywords
        importance_keywords = {
            "favorite": 0.2, "love": 0.15, "hate": 0.15, "prefer": 0.1,
            "always": 0.1, "never": 0.1, "obsessed": 0.2, "creator": 0.3,
            "minecraft": 0.1, "gaming": 0.1
        }

        content_lower = content.lower()
        keyword_boost = sum(boost for keyword, boost in importance_keywords.items()
                          if keyword in content_lower)

        # Context-based adjustments
        context_boost = 0.0
        if context:
            if context.get("user_initiated"): context_boost += 0.1
            if context.get("repeated_mention"): context_boost += 0.15
            if context.get("emotional_context"): context_boost += 0.1

        # Calculate final score (capped at 1.0)
        final_score = min(1.0, base_score + keyword_boost + context_boost)

        return final_score

    def _check_for_conflicts(self, new_memory: Memory):
        """Check if new memory conflicts with existing memories"""

        # Only check for conflicts in certain memory types
        conflict_types = [MemoryTier.PREFERENCES, MemoryTier.LONG_TERM_FACTS]
        if new_memory.memory_type not in conflict_types:
            return

        # Search for potentially conflicting memories
        similar_memories = self._find_similar_memories(new_memory.content, new_memory.memory_type)

        conflicts = []
        for memory in similar_memories:
            if self._are_conflicting(new_memory.content, memory.content):
                conflicts.append(memory.id)

        if conflicts:
            logger.info(f"Detected {len(conflicts)} conflicting memories for: {new_memory.content[:50]}...")
            self._handle_memory_conflict(new_memory.id, conflicts)

    def _are_conflicting(self, content1: str, content2: str) -> bool:
        """Determine if two memory contents are conflicting"""

        # Simple conflict detection patterns
        conflict_patterns = [
            ("favorite", "favorite"),  # Two different favorites
            ("prefers", "prefers"),    # Two different preferences
            ("loves", "hates"),        # Love vs hate
            ("always", "never"),       # Always vs never
            ("is", "is not"),          # Direct contradictions
        ]

        content1_lower = content1.lower()
        content2_lower = content2.lower()

        # Check for conflicting patterns
        for pattern1, pattern2 in conflict_patterns:
            if pattern1 in content1_lower and pattern2 in content2_lower:
                # Extract the subjects to see if they're about the same thing
                if self._same_subject(content1, content2):
                    return True

        return False

    def _same_subject(self, content1: str, content2: str) -> bool:
        """Check if two contents are about the same subject"""

        # Extract key nouns/subjects
        subjects = ["block", "food", "game", "color", "music", "movie", "book"]

        content1_lower = content1.lower()
        content2_lower = content2.lower()

        # If both mention the same subject category, they might conflict
        for subject in subjects:
            if subject in content1_lower and subject in content2_lower:
                return True

        return False

    def _handle_memory_conflict(self, new_memory_id: str, conflicting_ids: List[str]):
        """Handle detected memory conflicts through consolidation"""

        # Get all memories involved in conflict
        all_memory_ids = [new_memory_id] + conflicting_ids
        memories = [self._get_memory_by_id(mid) for mid in all_memory_ids]
        memories = [m for m in memories if m]  # Filter out None values

        if len(memories) < 2:
            return

        # Sort by importance and recency
        memories.sort(key=lambda m: (m.importance_score, m.recency_score), reverse=True)

        # If we have enough conflicts, trigger consolidation
        if len(memories) >= self.consolidation_threshold:
            self._consolidate_memories(memories)
        else:
            # Just log the conflict for now
            self._log_conflict(memories)

    def _consolidate_memories(self, conflicting_memories: List[Memory]):
        """Consolidate conflicting memories into a single authoritative memory"""

        logger.info(f"Consolidating {len(conflicting_memories)} conflicting memories")

        # Use LLM to resolve conflicts (if available)
        if hasattr(self, 'llm') and self.llm:
            consolidated_content = self._llm_consolidate(conflicting_memories)
        else:
            # Fallback: use most important + recent memory
            consolidated_content = conflicting_memories[0].content

        # Calculate consolidated importance (max of all memories)
        max_importance = max(m.importance_score for m in conflicting_memories)

        # Create consolidated memory
        consolidated_id = self.store_memory(
            content=consolidated_content,
            memory_type=conflicting_memories[0].memory_type,
            importance_score=min(1.0, max_importance + 0.1),  # Slight boost for being consolidated
            user_id=conflicting_memories[0].user_id,
            context={"consolidated": True, "source_count": len(conflicting_memories)}
        )

        # Mark old memories as superseded
        for memory in conflicting_memories:
            self._mark_superseded(memory.id, consolidated_id)

        # Log consolidation
        self._log_consolidation(conflicting_memories, consolidated_id)

    def _llm_consolidate(self, memories: List[Memory]) -> str:
        """Use LLM to intelligently consolidate conflicting memories"""

        memory_texts = [f"- {m.content} (importance: {m.importance_score:.2f}, created: {m.created_at.strftime('%Y-%m-%d')})"
                       for m in memories]

        prompt = f"""
        The following memories about Luna (an AI assistant) are conflicting. Please consolidate them into a single,
        accurate statement that represents Luna's current state/preference. Consider:
        - More recent memories may override older ones
        - Higher importance scores indicate more reliable information
        - Luna's personality should remain consistent

        Conflicting memories:
        {chr(10).join(memory_texts)}

        Consolidated memory (one sentence):
        """

        try:
            # This would use your LLM client
            response = self.llm.invoke(prompt)
            return response.strip()
        except Exception as e:
            logger.error(f"LLM consolidation failed: {e}")
            # Fallback to most important memory
            return max(memories, key=lambda m: m.importance_score).content

    def retrieve_memories(self, query: str, memory_types: List[str] = None,
                         max_results: int = 15, user_id: str = None,
                         conversation_context: List[str] = None,
                         exclude_recent_topics: bool = True) -> List[Memory]:
        """Retrieve relevant memories with enhanced scoring and echo prevention"""

        # Default to all memory types except working memory
        if memory_types is None:
            memory_types = [MemoryTier.CORE_PERSONALITY, MemoryTier.LONG_TERM_FACTS,
                          MemoryTier.PREFERENCES, MemoryTier.RECENT_INTERACTIONS]

        # Get candidate memories with expanded search
        candidates = self._search_memories_enhanced(query, memory_types, user_id, max_results * 3)

        # Extract current conversation topics for echo prevention
        current_topics = self._extract_topics(query, conversation_context or [])

        # Score and rank memories with enhanced algorithm
        scored_memories = []
        seen_content_hashes = set()  # For deduplication

        for memory in candidates:
            # Skip if this memory is too similar to already selected ones
            content_hash = self._get_content_hash(memory.content)
            if content_hash in seen_content_hashes:
                continue

            # Update recency score
            self._update_recency_score(memory)

            # Calculate enhanced relevance score
            relevance_score = self._calculate_enhanced_relevance(query, memory.content, current_topics)

            # Calculate echo penalty (reduce score if memory echoes current conversation)
            echo_penalty = 0.0
            if exclude_recent_topics:
                echo_penalty = self._calculate_echo_penalty(memory.content, current_topics)

            # Calculate diversity bonus (reward memories that add new information)
            diversity_bonus = self._calculate_diversity_bonus(memory, [m for m, _ in scored_memories])

            # Enhanced combined score with multiple factors
            combined_score = (
                memory.importance_score * 0.35 +           # Importance
                memory.recency_score * 0.20 +              # Recency
                relevance_score * 0.30 +                   # Relevance
                diversity_bonus * 0.10 +                   # Diversity
                (memory.access_count / 100.0) * 0.05       # Access frequency (normalized)
            ) - echo_penalty                               # Subtract echo penalty

            # Only include memories with positive scores
            if combined_score > 0.1:
                scored_memories.append((memory, combined_score))
                seen_content_hashes.add(content_hash)

        # Sort by combined score and return top results
        scored_memories.sort(key=lambda x: x[1], reverse=True)

        # Update access counts for returned memories
        top_memories = [memory for memory, score in scored_memories[:max_results]]
        for memory in top_memories:
            self._update_access_count(memory.id)

        logger.info(f"Retrieved {len(top_memories)} memories from {len(candidates)} candidates (echo prevention: {exclude_recent_topics})")
        return top_memories

    # Helper methods for database operations
    def _store_memory_db(self, memory: Memory):
        """Store memory in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO enhanced_memories
            (id, content, memory_type, importance_score, recency_score, access_count,
             last_accessed, created_at, user_id, context, consolidated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            memory.id, memory.content, memory.memory_type, memory.importance_score,
            memory.recency_score, memory.access_count, memory.last_accessed,
            memory.created_at, memory.user_id, json.dumps(memory.context), memory.consolidated
        ))

        conn.commit()
        conn.close()

    def _get_memory_by_id(self, memory_id: str) -> Optional[Memory]:
        """Retrieve memory by ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM enhanced_memories WHERE id = ?", (memory_id,))
        row = cursor.fetchone()
        conn.close()

        if not row:
            return None

        return Memory(
            id=row[0], content=row[1], memory_type=row[2], importance_score=row[3],
            recency_score=row[4], access_count=row[5],
            last_accessed=datetime.fromisoformat(row[6]),
            created_at=datetime.fromisoformat(row[7]), user_id=row[8],
            context=json.loads(row[9]) if row[9] else {}, consolidated=bool(row[10])
        )

    def _find_similar_memories(self, content: str, memory_type: str) -> List[Memory]:
        """Find memories similar to given content"""
        # Simple keyword-based similarity for now
        # In production, would use semantic similarity with embeddings

        keywords = content.lower().split()
        similar_memories = []

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Search for memories containing similar keywords
        for keyword in keywords:
            if len(keyword) > 3:  # Skip short words
                cursor.execute("""
                    SELECT * FROM enhanced_memories
                    WHERE memory_type = ? AND LOWER(content) LIKE ?
                    AND superseded_by IS NULL
                """, (memory_type, f"%{keyword}%"))

                rows = cursor.fetchall()
                for row in rows:
                    memory = Memory(
                        id=row[0], content=row[1], memory_type=row[2], importance_score=row[3],
                        recency_score=row[4], access_count=row[5],
                        last_accessed=datetime.fromisoformat(row[6]),
                        created_at=datetime.fromisoformat(row[7]), user_id=row[8],
                        context=json.loads(row[9]) if row[9] else {}, consolidated=bool(row[10])
                    )
                    if memory not in similar_memories:
                        similar_memories.append(memory)

        conn.close()
        return similar_memories

    def _search_memories(self, query: str, memory_types: List[str], user_id: str = None) -> List[Memory]:
        """Search memories by query and types"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Build query
        type_placeholders = ','.join(['?' for _ in memory_types])
        base_query = f"""
            SELECT * FROM enhanced_memories
            WHERE memory_type IN ({type_placeholders})
            AND superseded_by IS NULL
        """
        params = memory_types

        if user_id:
            base_query += " AND (user_id = ? OR user_id = 'system')"
            params.append(user_id)

        # Add text search
        query_words = query.lower().split()
        for word in query_words:
            if len(word) > 2:
                base_query += " AND LOWER(content) LIKE ?"
                params.append(f"%{word}%")

        base_query += " ORDER BY importance_score DESC, created_at DESC LIMIT 50"

        cursor.execute(base_query, params)
        rows = cursor.fetchall()
        conn.close()

        memories = []
        for row in rows:
            memory = Memory(
                id=row[0], content=row[1], memory_type=row[2], importance_score=row[3],
                recency_score=row[4], access_count=row[5],
                last_accessed=datetime.fromisoformat(row[6]),
                created_at=datetime.fromisoformat(row[7]), user_id=row[8],
                context=json.loads(row[9]) if row[9] else {}, consolidated=bool(row[10])
            )
            memories.append(memory)

        return memories

    def _update_recency_score(self, memory: Memory):
        """Update recency score based on age"""
        now = datetime.now()
        age_days = (now - memory.created_at).days

        # Exponential decay: score = e^(-age_days / 30)
        # Memories lose half their recency every 30 days
        import math
        memory.recency_score = math.exp(-age_days / 30.0)

    def _calculate_relevance(self, query: str, content: str) -> float:
        """Calculate relevance score between query and content (legacy method)"""
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        if not query_words:
            return 0.0

        # Simple Jaccard similarity
        intersection = len(query_words.intersection(content_words))
        union = len(query_words.union(content_words))

        return intersection / union if union > 0 else 0.0

    def _calculate_enhanced_relevance(self, query: str, content: str, current_topics: List[str]) -> float:
        """Calculate enhanced relevance score with semantic understanding"""

        # Basic keyword relevance (improved)
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())

        # Exact word matches
        exact_matches = len(query_words.intersection(content_words))
        total_query_words = len(query_words)

        if total_query_words == 0:
            return 0.0

        # Base relevance from exact matches
        base_relevance = exact_matches / total_query_words

        # Semantic relevance using word relationships
        semantic_score = self._calculate_semantic_relevance(query, content)

        # Context relevance (how well this memory fits the conversation context)
        context_score = self._calculate_context_relevance(content, current_topics)

        # Combine scores with weights
        enhanced_relevance = (
            base_relevance * 0.4 +      # Direct word matches
            semantic_score * 0.4 +      # Semantic similarity
            context_score * 0.2         # Contextual fit
        )

        return min(1.0, enhanced_relevance)

    def _calculate_semantic_relevance(self, query: str, content: str) -> float:
        """Calculate semantic relevance using word relationships and patterns"""

        # Define semantic word groups for better matching
        semantic_groups = {
            'gaming': ['minecraft', 'game', 'gaming', 'play', 'server', 'mod', 'block', 'craft'],
            'preferences': ['favorite', 'love', 'like', 'prefer', 'enjoy', 'obsessed', 'hate', 'dislike'],
            'personality': ['personality', 'trait', 'character', 'behavior', 'style', 'way'],
            'friends': ['friend', 'buddy', 'pal', 'mate', 'companion', 'person', 'user'],
            'activities': ['activity', 'doing', 'action', 'task', 'work', 'project', 'hobby'],
            'emotions': ['happy', 'sad', 'excited', 'angry', 'frustrated', 'joy', 'emotion', 'feel']
        }

        query_lower = query.lower()
        content_lower = content.lower()

        semantic_score = 0.0
        matches = 0

        # Check for semantic group matches
        for group_name, words in semantic_groups.items():
            query_has_group = any(word in query_lower for word in words)
            content_has_group = any(word in content_lower for word in words)

            if query_has_group and content_has_group:
                semantic_score += 0.3  # Boost for semantic group match
                matches += 1

        # Check for related concepts (simple patterns)
        concept_patterns = [
            (['what', 'who', 'where', 'when', 'why', 'how'], ['answer', 'explain', 'tell', 'know']),
            (['create', 'make', 'build'], ['created', 'made', 'built', 'construction']),
            (['remember', 'recall', 'memory'], ['remembered', 'recalled', 'past', 'before'])
        ]

        for query_patterns, content_patterns in concept_patterns:
            query_has_pattern = any(pattern in query_lower for pattern in query_patterns)
            content_has_pattern = any(pattern in content_lower for pattern in content_patterns)

            if query_has_pattern and content_has_pattern:
                semantic_score += 0.2
                matches += 1

        # Normalize by number of potential matches
        if matches > 0:
            semantic_score = semantic_score / max(1, matches * 0.5)

        return min(1.0, semantic_score)

    def _calculate_context_relevance(self, content: str, current_topics: List[str]) -> float:
        """Calculate how well this memory fits the current conversation context"""

        if not current_topics:
            return 0.5  # Neutral score if no context

        content_lower = content.lower()

        # Check if memory relates to current topics (but not too directly to avoid echo)
        topic_relevance = 0.0
        for topic in current_topics:
            topic_words = topic.lower().split()

            # Partial matches are good (shows relevance without being echo)
            partial_matches = sum(1 for word in topic_words if word in content_lower and len(word) > 3)
            if partial_matches > 0:
                # Reward some relevance but not complete overlap
                topic_relevance += min(0.3, partial_matches * 0.1)

        return min(1.0, topic_relevance)

    def _extract_topics(self, query: str, conversation_context: List[str]) -> List[str]:
        """Extract key topics from query and recent conversation"""

        topics = []

        # Extract topics from current query
        query_words = [word.lower() for word in query.split() if len(word) > 3]
        topics.extend(query_words)

        # Extract topics from recent conversation context
        for context_msg in conversation_context[-3:]:  # Last 3 messages
            context_words = [word.lower() for word in context_msg.split() if len(word) > 3]
            topics.extend(context_words)

        # Remove duplicates and common words
        common_words = {'that', 'this', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'}

        unique_topics = list(set(topic for topic in topics if topic not in common_words))

        return unique_topics[:10]  # Limit to top 10 topics

    def _calculate_echo_penalty(self, content: str, current_topics: List[str]) -> float:
        """Calculate penalty for memories that echo current conversation topics"""

        if not current_topics:
            return 0.0

        content_lower = content.lower()
        echo_score = 0.0

        # Check for direct topic echoes
        for topic in current_topics:
            if topic in content_lower:
                # Higher penalty for exact matches of recent topics
                echo_score += 0.2

        # Check for phrase echoes (multiple topic words together)
        content_words = content_lower.split()
        for i in range(len(content_words) - 1):
            phrase = f"{content_words[i]} {content_words[i+1]}"
            for topic in current_topics:
                if len(topic.split()) > 1 and topic in phrase:
                    echo_score += 0.3  # Higher penalty for phrase matches

        # Cap the penalty
        return min(0.5, echo_score)

    def _calculate_diversity_bonus(self, memory: Memory, already_selected: List[Memory]) -> float:
        """Calculate bonus for memories that add diverse information"""

        if not already_selected:
            return 0.1  # Small bonus for first memory

        memory_content_lower = memory.content.lower()

        # Check how different this memory is from already selected ones
        diversity_score = 0.1  # Base diversity bonus

        for selected_memory in already_selected:
            selected_content_lower = selected_memory.content.lower()

            # Calculate content similarity
            memory_words = set(memory_content_lower.split())
            selected_words = set(selected_content_lower.split())

            if memory_words and selected_words:
                overlap = len(memory_words.intersection(selected_words))
                total = len(memory_words.union(selected_words))
                similarity = overlap / total if total > 0 else 0

                # Reduce diversity bonus based on similarity
                diversity_score -= similarity * 0.1

        # Bonus for different memory types
        selected_types = {m.memory_type for m in already_selected}
        if memory.memory_type not in selected_types:
            diversity_score += 0.05

        return max(0.0, diversity_score)

    def _get_content_hash(self, content: str) -> str:
        """Generate a hash for content deduplication"""
        import hashlib

        # Normalize content for hashing
        normalized = ' '.join(content.lower().split())

        # Create hash of first 100 characters (to catch very similar content)
        content_snippet = normalized[:100]
        return hashlib.md5(content_snippet.encode()).hexdigest()

    def _search_memories_enhanced(self, query: str, memory_types: List[str],
                                 user_id: str = None, max_candidates: int = 50) -> List[Memory]:
        """Enhanced memory search with better candidate selection"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Build more sophisticated query
        type_placeholders = ','.join(['?' for _ in memory_types])
        base_query = f"""
            SELECT * FROM enhanced_memories
            WHERE memory_type IN ({type_placeholders})
            AND superseded_by IS NULL
        """
        params = memory_types

        if user_id:
            base_query += " AND (user_id = ? OR user_id = 'system' OR user_id = 'luna')"
            params.append(user_id)

        # Enhanced text search with multiple strategies
        query_words = [word.lower() for word in query.split() if len(word) > 2]

        if query_words:
            # Strategy 1: Any word match (broader search)
            word_conditions = []
            for word in query_words[:5]:  # Limit to first 5 words
                word_conditions.append("LOWER(content) LIKE ?")
                params.append(f"%{word}%")

            if word_conditions:
                base_query += f" AND ({' OR '.join(word_conditions)})"

        # Order by importance and recency, but get more candidates
        base_query += " ORDER BY importance_score DESC, created_at DESC LIMIT ?"
        params.append(max_candidates)

        cursor.execute(base_query, params)
        rows = cursor.fetchall()
        conn.close()

        memories = []
        for row in rows:
            memory = Memory(
                id=row[0], content=row[1], memory_type=row[2], importance_score=row[3],
                recency_score=row[4], access_count=row[5],
                last_accessed=datetime.fromisoformat(row[6]),
                created_at=datetime.fromisoformat(row[7]), user_id=row[8],
                context=json.loads(row[9]) if row[9] else {}, consolidated=bool(row[10])
            )
            memories.append(memory)

        return memories

    def learn_from_conversation(self, messages: List[Dict], user_id: str = None,
                               conversation_type: str = "voice") -> List[str]:
        """Intelligently extract and store important information from conversation"""

        stored_memories = []

        try:
            # Analyze recent messages for learnable content
            for i, message in enumerate(messages[-5:]):  # Look at last 5 messages
                role = message.get('role', '')
                content = message.get('content', '').strip()

                if not content or len(content) < 10:
                    continue

                # Extract different types of information based on role
                if role == 'user':
                    extracted = self._extract_user_information(content, user_id, conversation_type)
                    stored_memories.extend(extracted)
                elif role == 'assistant':
                    extracted = self._extract_luna_information(content, conversation_type)
                    stored_memories.extend(extracted)

        except Exception as e:
            logger.error(f"Error in learn_from_conversation: {e}")

        return stored_memories

    def _extract_user_information(self, content: str, user_id: str, conversation_type: str) -> List[str]:
        """Extract important information about users from their messages"""

        stored_memories = []
        content_lower = content.lower()

        # Pattern matching for different types of user information
        patterns = {
            'preferences': [
                (r'i (love|like|enjoy|prefer|am obsessed with) (.+)', 'prefers'),
                (r'my favorite (.+) is (.+)', 'favorite'),
                (r'i hate|dislike|can\'t stand (.+)', 'dislikes'),
                (r'i always (.+)', 'habit'),
                (r'i never (.+)', 'avoids')
            ],
            'facts': [
                (r'i am (.+)', 'is'),
                (r'i work as (.+)', 'works as'),
                (r'i live in (.+)', 'lives in'),
                (r'my (.+) is (.+)', 'has'),
                (r'i have (.+)', 'has'),
                (r'i study (.+)', 'studies')
            ],
            'activities': [
                (r'i play (.+)', 'plays'),
                (r'i\'m playing (.+)', 'currently playing'),
                (r'i\'m working on (.+)', 'working on'),
                (r'i\'m building (.+)', 'building')
            ]
        }

        import re

        for category, pattern_list in patterns.items():
            for pattern, relationship in pattern_list:
                matches = re.findall(pattern, content_lower)
                for match in matches:
                    if isinstance(match, tuple):
                        subject, obj = match
                        memory_content = f"User {relationship} {obj.strip()}"
                    else:
                        memory_content = f"User {relationship} {match.strip()}"

                    # Determine memory type and importance
                    if category == 'preferences':
                        memory_type = MemoryTier.PREFERENCES
                        importance = 0.7
                    elif category == 'facts':
                        memory_type = MemoryTier.LONG_TERM_FACTS
                        importance = 0.8
                    else:
                        memory_type = MemoryTier.RECENT_INTERACTIONS
                        importance = 0.6

                    # Store the memory
                    memory_id = self.store_memory(
                        content=memory_content,
                        memory_type=memory_type,
                        importance_score=importance,
                        user_id=user_id,
                        context={
                            'extracted_from': 'user_message',
                            'conversation_type': conversation_type,
                            'category': category,
                            'original_content': content[:100]
                        }
                    )
                    stored_memories.append(memory_id)
                    logger.info(f"Learned about user: {memory_content}")

        return stored_memories

    def _extract_luna_information(self, content: str, conversation_type: str) -> List[str]:
        """Extract important information about Luna from her own responses"""

        stored_memories = []
        content_lower = content.lower()

        # Pattern matching for Luna's self-statements
        luna_patterns = [
            (r'i (love|like|enjoy|prefer|am obsessed with) (.+)', 'prefers'),
            (r'my favorite (.+) is (.+)', 'favorite'),
            (r'i (hate|dislike|can\'t stand) (.+)', 'dislikes'),
            (r'i always (.+)', 'always'),
            (r'i never (.+)', 'never'),
            (r'i am (.+)', 'is'),
            (r'i tend to (.+)', 'tends to'),
            (r'i usually (.+)', 'usually')
        ]

        import re

        for pattern, relationship in luna_patterns:
            matches = re.findall(pattern, content_lower)
            for match in matches:
                if isinstance(match, tuple):
                    emotion, subject = match
                    memory_content = f"Luna {relationship} {subject.strip()}"
                else:
                    memory_content = f"Luna {relationship} {match.strip()}"

                # Store as core personality or preference
                memory_type = MemoryTier.CORE_PERSONALITY if relationship in ['is', 'always', 'never', 'tends to'] else MemoryTier.PREFERENCES

                memory_id = self.store_memory(
                    content=memory_content,
                    memory_type=memory_type,
                    importance_score=0.9,  # Luna's self-statements are very important
                    user_id="luna",
                    context={
                        'extracted_from': 'luna_response',
                        'conversation_type': conversation_type,
                        'self_reported': True,
                        'original_content': content[:100]
                    }
                )
                stored_memories.append(memory_id)
                logger.info(f"Learned about Luna: {memory_content}")

        return stored_memories

    def _update_access_count(self, memory_id: str):
        """Update access count for a memory"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE enhanced_memories
            SET access_count = access_count + 1, last_accessed = ?
            WHERE id = ?
        """, (datetime.now(), memory_id))

        conn.commit()
        conn.close()

    def _mark_superseded(self, old_memory_id: str, new_memory_id: str):
        """Mark an old memory as superseded by a new one"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE enhanced_memories
            SET superseded_by = ?
            WHERE id = ?
        """, (new_memory_id, old_memory_id))

        conn.commit()
        conn.close()

    def _log_conflict(self, memories: List[Memory]):
        """Log a memory conflict"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        conflict_id = str(uuid.uuid4())
        memory_ids = [m.id for m in memories]

        cursor.execute("""
            INSERT INTO memory_conflicts (id, topic, conflicting_memories, resolved_at, confidence_score)
            VALUES (?, ?, ?, NULL, NULL)
        """, (conflict_id, "auto_detected", json.dumps(memory_ids)))

        conn.commit()
        conn.close()

    def _log_consolidation(self, input_memories: List[Memory], output_memory_id: str):
        """Log a memory consolidation"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        consolidation_id = str(uuid.uuid4())
        input_ids = [m.id for m in input_memories]

        cursor.execute("""
            INSERT INTO consolidation_log
            (id, consolidation_type, input_memories, output_memory_id, performed_at, reasoning)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (consolidation_id, "conflict_resolution", json.dumps(input_ids),
              output_memory_id, datetime.now(), "Automatic conflict resolution"))

        conn.commit()
        conn.close()
