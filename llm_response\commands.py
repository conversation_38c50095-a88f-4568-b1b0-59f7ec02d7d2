import os
import logging
import re
import discord
import time
import httpx
import base64
import mimetypes
import asyncio
from openai import OpenAI # For LM Studio vision client

# Import necessary components from other modules within the package
from .config import (
    SCREENSHOT_COOLDOWN, CALL_NOTIFICATION_CHANNEL_ID_STR, # Removed USER_ALIASES
    LM_STUDIO_VISION_URL, LM_STUDIO_VISION_API_KEY, VISION_MODEL,
    IMAGE_ANALYSIS_MAX_TOKENS, IMAGE_ANALYSIS_TEMP, IMAGE_DOWNLOAD_TIMEOUT,
    CALL_MSG_MAX_OUTPUT_TOKENS, CALL_MSG_TEMP, TRANSCRIPT_LOG_CHANNEL_ID,
    PROMPT_LOG_CHANNEL_ID # Added for process_image_mention logging
)
from .initialization import get_lm_studio_client, create_lm_studio_client
from .decision import safe_model_call
# Import process_user_message carefully to avoid circular dependency if possible
# If commands.py is imported by processing.py, this will cause issues.
# Consider refactoring process_image_mention or passing process_user_message as an arg.
# For now, assume it might be imported later or handled differently.
# from .processing import process_user_message # Commented out for now

# Import necessary components from outside the package
from text_to_speech import StreamingSpeechPlayer
from utils import remove_emojis, format_datetime # format_datetime might not be needed here anymore

logger = logging.getLogger(__name__)

# Track screenshot usage to prevent abuse
last_screenshot_time = 0

# --- Screenshot Command Handling (Requires Multimodal Model - Placeholder/Disabled) ---
async def handle_screenshot_command(sink, text, user_id, display_name=None, text_channel=None):
    """Placeholder for screenshot command. Requires a multimodal model."""
    global last_screenshot_time

    # Rate limit screenshots
    current_time = time.time()
    if current_time - last_screenshot_time < SCREENSHOT_COOLDOWN:
        cooldown_message = f"Please wait a moment before requesting another screenshot."

        if text_channel:
            await text_channel.send(cooldown_message)
        else:
            # Voice response
            voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients else None
            if voice_client:
                if voice_client.is_playing():
                    voice_client.stop()

                sink.is_speaking = True
                player = StreamingSpeechPlayer(
                    voice_client,
                    after_play_callback=lambda e: sink._after_play(e, user_id)
                )
                await player.start()
                await player.add_text(cooldown_message)
                await player.finalize()

        return True # Indicate cooldown was triggered

    # Extract custom analysis prompt if present - improved patterns to catch more query formats
    custom_prompt = None
    prompt_patterns = [
        # Standard patterns
        r"screenshot (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        r"take a screenshot (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        r"capture (?:my|the) screen (?:and|&) (?:analyze|tell me about|describe|explain|what do you see in|look at|find|search for|check for|see if there's|can you see|is there) (.*)",
        # Direct search queries
        r"screenshot (?:and|&) (find|look for|search for|locate|spot|identify) (.*)",
        r"take a screenshot (?:and|&) (find|look for|search for|locate|spot|identify) (.*)",
        r"capture (?:my|the) screen (?:and|&) (find|look for|search for|locate|spot|identify) (.*)",
        # Question formats
        r"screenshot (?:and|&) (tell me if|check if|see if|can you tell if|is there|are there) (.*)",
        r"take a screenshot (?:and|&) (tell me if|check if|see if|can you tell if|is there|are there) (.*)",
        r"capture (?:my|the) screen (?:and|&) (tell me if|check if|see if|can you tell if|is there|are there) (.*)",
        # Simple formats with prepositions
        r"screenshot (?:and tell me about|and look for|and find) (.*)",
        r"take a screenshot (?:and tell me about|and look for|and find) (.*)",
        r"capture (?:my|the) screen (?:and tell me about|and look for|and find) (.*)",
        # Direct questions after screenshot command
        r"(?:screenshot|take a screenshot|capture my screen).*?(where is|what is|how many|can you find|do you see) (.*)"
    ]

    for pattern in prompt_patterns:
        match = re.search(pattern, text.lower())
        if match:
            if len(match.groups()) == 1:
                custom_prompt = match.group(1).strip() # Single group patterns
            elif len(match.groups()) >= 2:
                action = match.group(1).strip() # Double group patterns - combine action with target
                target = match.group(2).strip()
                custom_prompt = f"{action} {target}"
            break

    # Check for direct "look for X" pattern anywhere in text
    if not custom_prompt:
        look_patterns = [
            r"look for (.*?)(?:\.|\?|$|in the|on the)", r"find (.*?)(?:\.|\?|$|in the|on the)",
            r"search for (.*?)(?:\.|\?|$|in the|on the)", r"see if (.*?)(?:\.|\?|$|in the|on the)",
            r"check if (.*?)(?:\.|\?|$|in the|on the)", r"tell me if (.*?)(?:\.|\?|$|in the|on the)",
            r"is there (.*?)(?:\.|\?|$|in the|on the)"
        ]
        for pattern in look_patterns:
            match = re.search(pattern, text.lower())
            if match:
                custom_prompt = match.group(1).strip()
                break

    # If still no specific prompt, but text contains question words after "screenshot", try to extract the question
    if not custom_prompt and any(q in text.lower() for q in ["what", "where", "how", "is there", "are there", "can you"]):
        text_parts = text.lower().split("screenshot")
        if len(text_parts) > 1:
            question_part = text_parts[1].strip() # Extract the part after "screenshot"
            question_part = re.sub(r'^(?:and|&|to)\s+', '', question_part) # Remove transition words
            if question_part:
                custom_prompt = question_part

    # Create a more specific analysis instruction if we have a custom prompt
    specific_instruction = ""
    if custom_prompt:
        specific_instruction = f"SPECIFIC SEARCH REQUEST: '{custom_prompt}'. Focus ONLY on this request, not on general description."
        logger.info(f"Extracted specific search query: '{custom_prompt}'")

    # Let the user know we're taking a screenshot
    processing_message = "Analyzing your screen..."
    if custom_prompt:
        processing_message = f"Looking specifically for '{custom_prompt}'..."

    if text_channel:
        await text_channel.send(processing_message)
    else:
        # Voice response
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(processing_message)
            await player.finalize()

    # --- SCREENSHOT ANALYSIS DISABLED ---
    # The current model might not be multimodal. Screenshot analysis needs a multimodal model.
    # result = await take_and_analyze_screenshot(custom_prompt, specific_instruction) # This function doesn't exist here
    result = {"success": False, "error": "Screenshot analysis is currently disabled (requires a multimodal model)."}
    # --- END DISABLED BLOCK ---

    # Update rate limit
    last_screenshot_time = time.time()

    if not result["success"]:
        error_message = f"I couldn't analyze your screen: {result.get('error', 'Unknown error')}"
        if text_channel:
            await text_channel.send(error_message)
        else:
            # Voice response for error
            voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients else None
            if voice_client:
                if voice_client.is_playing(): voice_client.stop()
                sink.is_speaking = True
                player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
                await player.start()
                await player.add_text(error_message)
                await player.finalize()
                # Add to history (assuming sink has _add_to_history)
                if hasattr(sink, '_add_to_history'):
                    await sink._add_to_history("assistant", error_message, "assistant")
        return True # Indicate screenshot command was handled (even if failed)

    # --- Process successful result (Currently unreachable due to disabled analysis) ---
    screenshot_data = result["screenshot"]
    analysis = result["analysis"]

    # If we have a specific search request, make sure the response addresses it directly
    if custom_prompt and not any(phrase in analysis.lower() for phrase in ["not found", "couldn't find", "don't see", "no sign of"]):
        if not re.search(rf'\b{re.escape(custom_prompt)}\b', analysis.lower()):
            direct_answer = f"About your request to find '{custom_prompt}': "
            analysis = direct_answer + analysis

    # Deliver the response
    if text_channel:
        try:
            await text_channel.send(file=discord.File(screenshot_data["filename"]))
            await text_channel.send(analysis)
            if hasattr(sink, '_add_to_history'): # Check if sink exists and has method
                 await sink._add_to_history("assistant", analysis, "assistant", channel_type="text", channel_id=str(text_channel.id))
        except Exception as e:
            logger.error(f"Error sending screenshot to text channel: {e}")
            await text_channel.send(f"I analyzed your screen, but couldn't send the image: {str(e)}")
            await text_channel.send(analysis)
    else:
        # Voice response
        voice_client = sink.bot.voice_clients[0] if sink.bot.voice_clients else None
        if voice_client:
            if voice_client.is_playing(): voice_client.stop()
            sink.is_speaking = True
            player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
            await player.start()
            await player.add_text(analysis)
            await player.finalize()
            if hasattr(sink, '_add_to_history'):
                await sink._add_to_history("assistant", analysis, "assistant")

    return True # Indicate screenshot command was handled

# --- Call Message Generation ---
async def generate_call_message(target_name: str, system_prompt: str) -> str:
    """Generates a short, engaging message body for calling a user."""
    lm_studio_client = get_lm_studio_client() # Get the initialized client
    if not lm_studio_client:
        logger.error("Cannot generate call message: LM Studio client not initialized.")
        return f"Hey {target_name}, someone's asking for you!" # Fallback

    prompt = f"""You need to generate a short, casual message body to notify '{target_name}' that someone in the voice chat is asking for them. Keep it brief and friendly, encouraging them to join the voice channel.

    Examples:
    - "Hey {target_name}, hop in the voice channel! We need you."
    - "Yo {target_name}, someone's asking for you in voice chat."
    - "{target_name}! Get in here, you're being summoned!"
    - "Paging {target_name}, you're wanted in the voice channel."

    Generate ONLY the message body below (don't include the name again unless it fits naturally):
    """

    try:
        # Use a stateless call for this simple generation
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ]

        response = await asyncio.to_thread(
            lm_studio_client.chat.completions.create,
            model=os.environ.get("LM_STUDIO_MODEL_NAME", "luna:latest"),
            messages=messages,
            temperature=CALL_MSG_TEMP,
            max_tokens=CALL_MSG_MAX_OUTPUT_TOKENS
        )

        message_body = response.choices[0].message.content.strip()
        if not message_body:
            raise ValueError("LLM returned empty message body.")
        logger.info(f"Generated call message body for {target_name}: {message_body}")
        return message_body
    except Exception as e:
        logger.error(f"Error generating call message body for {target_name}: {e}", exc_info=True)
        # Fallback message
        return f"someone's asking for you!"

# --- Image Analysis (LM Studio - Placeholder/Experimental) ---
async def analyze_image_url_lm_studio(image_url: str) -> str | None:
    """Analyzes an image URL using an LM Studio multimodal model (if configured)."""
    if not LM_STUDIO_VISION_URL or not VISION_MODEL:
        logger.warning("LM Studio vision URL or model not configured. Skipping image analysis.")
        return None

    # Create a specific client for vision, potentially different from the decision client
    # Using create_lm_studio_client assumes the same base URL/key, adjust if needed
    client = OpenAI(api_key=LM_STUDIO_VISION_API_KEY, base_url=LM_STUDIO_VISION_URL)

    logger.info(f"Attempting image analysis via LM Studio ({VISION_MODEL}) for URL: {image_url}")

    # Download image data
    image_data = None
    mime_type = None
    try:
        async with httpx.AsyncClient() as http_client:
            response = await http_client.get(image_url, timeout=IMAGE_DOWNLOAD_TIMEOUT)
            response.raise_for_status()
            image_data = response.content
            mime_type = mimetypes.guess_type(image_url)[0]
            if not mime_type and 'content-type' in response.headers:
                mime_type = response.headers['content-type'].split(';')[0]
            if not mime_type:
                 mime_type = 'image/jpeg' # Default guess
                 logger.warning(f"Could not determine MIME type for {image_url}, defaulting to {mime_type}")
    except httpx.RequestError as exc:
        logger.error(f"Error downloading image {image_url}: {exc}")
        return f"(Error: Could not download image: {exc})"
    except Exception as exc:
        logger.error(f"Unexpected error downloading image {image_url}: {exc}")
        return f"(Error: Unexpected issue downloading image: {exc})"

    if not image_data:
        return "(Error: Image data could not be retrieved)"

    # Encode image to base64
    base64_image = base64.b64encode(image_data).decode('utf-8')
    data_url = f"data:{mime_type};base64,{base64_image}"

    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "Describe this image briefly."},
                {"type": "image_url", "image_url": {"url": data_url}}
            ],
        }
    ]

    try:
        analysis_start_time = time.monotonic()
        response = await safe_model_call(
            client=client,
            model=VISION_MODEL,
            messages=messages,
            temperature=IMAGE_ANALYSIS_TEMP,
            max_tokens=IMAGE_ANALYSIS_MAX_TOKENS
        )
        analysis_end_time = time.monotonic()
        logger.info(f"LM STUDIO IMAGE ANALYSIS API CALL ({VISION_MODEL}): Completed in {analysis_end_time - analysis_start_time:.6f}s")

        if not response or not response.choices or response.choices[0].finish_reason == "error":
            logger.error(f"Image analysis LLM call failed or returned error structure for URL: {image_url}")
            return "(Error: Image analysis failed)"

        analysis_text = response.choices[0].message.content.strip()
        logger.info(f"LM Studio Image Analysis Result: {analysis_text[:100]}...")
        return analysis_text

    except Exception as e:
        logger.error(f"LM Studio image analysis error: {e}", exc_info=True)
        return f"(Error: Image analysis failed: {e})"


# --- Process Image Mention (Combined Image Analysis + Response) ---
# NOTE: This function depends on process_user_message, creating potential circular imports.
# It might be better placed in main.py or refactored.
async def process_image_mention(*, message, image_url, bot, system_prompt, process_user_message_func):
    """Process a message that mentions Luna and includes an image attachment.
    This function analyzes the image and generates a response in one step.

    Args:
        message: The Discord message object
        image_url: URL of the image to analyze
        bot: The Discord bot instance
        system_prompt: System prompt for the LLM
        process_user_message_func: The actual process_user_message function to call.
    """
    logger.info(f"Processing image mention from {message.author.display_name} with URL: {image_url}")

    # Step 1: Analyze the image
    image_analysis = await analyze_image_url_lm_studio(image_url)

    if not image_analysis:
        await message.channel.send("Sorry, I couldn't analyze that image. My vision capabilities might be offline.")
        return

    logger.info(f"Image analysis complete: {image_analysis[:100]}...")

    # Step 2: Get or create a chat session for this channel
    # Ensure bot has chat_sessions attribute initialized
    if not hasattr(bot, 'chat_sessions'):
        bot.chat_sessions = {} # Initialize if missing

    session_key = f"text_{message.channel.id}"
    chat_session = bot.chat_sessions.get(session_key)
    lm_studio_client = get_lm_studio_client() # Get the initialized client

    if not chat_session and lm_studio_client:
        logger.info(f"Creating new chat session for channel {message.channel.id}")
        # Create a session object with the system prompt
        session_id = f"channel_{message.channel.id}_{int(time.time())}"
        chat_session = {
            "id": session_id,
            "messages": []
        }

        # Add system prompt if available
        if system_prompt:
            chat_session["messages"].append({"role": "system", "content": system_prompt})

        bot.chat_sessions[session_key] = chat_session
    elif not lm_studio_client:
        logger.error("Cannot create chat session for image mention: LM Studio client not initialized.")
        await message.channel.send("Sorry, I can't process this right now due to an internal issue.")
        return


    # Step 3: Process the message with the image analysis using the passed function
    await process_user_message_func(
        bot=bot,
        text=message.content,
        user_id=message.author.id,
        conversation_history=[],  # History is managed by the session for stateful calls
        system_prompt=system_prompt,
        sink=None,  # No sink for text messages
        force_respond=True,  # Always respond to image mentions
        display_name=message.author.display_name,
        text_channel=message.channel,
        image_analysis_text=image_analysis,  # Pass the analysis
        chat_session=chat_session  # Use the stateful session
    )

    logger.info(f"Completed processing image mention from {message.author.display_name}")