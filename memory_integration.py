"""
Integration module for Enhanced Memory System with <PERSON>'s existing RAG
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from enhanced_memory_system import EnhancedMemorySystem, MemoryTier, Memory

logger = logging.getLogger(__name__)

class LunaMemoryIntegration:
    """
    Integration layer between <PERSON>'s existing RAG system and the enhanced memory system
    """

    def __init__(self, enhanced_memory: EnhancedMemorySystem, existing_rag_retriever=None):
        self.enhanced_memory = enhanced_memory
        self.existing_rag = existing_rag_retriever
        self.memory_learning_enabled = True

    async def retrieve_for_conversation(self, query: str, is_dm: bool = False,
                                      user_id: str = None, max_memories: int = 15,
                                      conversation_context: List[str] = None) -> List[Dict]:
        """
        Main retrieval method that combines enhanced memory with existing RAG
        """

        if is_dm:
            # For DMs, use enhanced personalized memory with echo prevention
            return await self._retrieve_dm_memories(query, user_id, max_memories, conversation_context)
        else:
            # For voice/guild channels, use hybrid approach with smart filtering
            return await self._retrieve_group_memories(query, user_id, max_memories, conversation_context)

    async def _retrieve_dm_memories(self, query: str, user_id: str, max_memories: int,
                                   conversation_context: List[str] = None) -> List[Dict]:
        """Retrieve memories optimized for DM conversations with echo prevention"""

        # Get enhanced memories with focus on personality and preferences
        memory_types = [
            MemoryTier.CORE_PERSONALITY,
            MemoryTier.PREFERENCES,
            MemoryTier.LONG_TERM_FACTS
        ]

        # Use enhanced retrieval with conversation context and echo prevention
        enhanced_memories = self.enhanced_memory.retrieve_memories(
            query=query,
            memory_types=memory_types,
            max_results=max_memories,
            user_id=user_id,
            conversation_context=conversation_context,
            exclude_recent_topics=True  # Enable echo prevention for DMs
        )

        # Convert to format expected by existing system with enhanced metadata
        formatted_memories = []
        for memory in enhanced_memories:
            formatted_memories.append({
                'id': memory.id,
                'role': self._determine_role(memory),
                'content': memory.content,
                'timestamp': memory.created_at.timestamp(),
                'importance': memory.importance_score,
                'memory_type': memory.memory_type,
                'source': 'enhanced',
                'access_count': memory.access_count,
                'user_specific': memory.user_id == user_id
            })

        logger.info(f"Retrieved {len(formatted_memories)} enhanced memories for DM (echo prevention enabled)")
        return formatted_memories

    async def _retrieve_group_memories(self, query: str, user_id: str, max_memories: int,
                                      conversation_context: List[str] = None) -> List[Dict]:
        """Retrieve memories for group conversations with smart filtering and echo prevention"""

        # Enhanced approach: prioritize enhanced memories with smart RAG fallback
        enhanced_count = max(max_memories // 2, 5)  # At least half from enhanced system
        rag_count = max_memories - enhanced_count

        # Get core personality and important facts from enhanced system with echo prevention
        enhanced_memories = self.enhanced_memory.retrieve_memories(
            query=query,
            memory_types=[MemoryTier.CORE_PERSONALITY, MemoryTier.LONG_TERM_FACTS, MemoryTier.PREFERENCES],
            max_results=enhanced_count,
            user_id=user_id,
            conversation_context=conversation_context,
            exclude_recent_topics=True  # Enable echo prevention
        )

        # Get broader context from existing RAG with deduplication
        rag_memories = []
        if self.existing_rag and self.existing_rag.is_ready and rag_count > 0:
            try:
                rag_results = await self.existing_rag.retrieve(query, top_k=rag_count * 2)  # Get more for filtering
                rag_memories = rag_results or []
            except Exception as e:
                logger.error(f"Error retrieving from existing RAG: {e}")

        # Combine and format with intelligent deduplication
        all_memories = []
        seen_content_hashes = set()

        # Add enhanced memories (higher priority)
        for memory in enhanced_memories:
            content_hash = self._get_content_hash(memory.content)
            if content_hash not in seen_content_hashes:
                all_memories.append({
                    'id': memory.id,
                    'role': self._determine_role(memory),
                    'content': memory.content,
                    'timestamp': memory.created_at.timestamp(),
                    'importance': memory.importance_score,
                    'memory_type': memory.memory_type,
                    'source': 'enhanced',
                    'access_count': memory.access_count
                })
                seen_content_hashes.add(content_hash)

        # Add RAG memories (lower priority, avoid duplicates and echoes)
        current_topics = self._extract_conversation_topics(query, conversation_context or [])

        for rag_memory in rag_memories:
            content = rag_memory.get('content', '')
            content_hash = self._get_content_hash(content)

            # Skip if duplicate or too similar to enhanced memories
            if content_hash in seen_content_hashes:
                continue

            # Skip if it echoes current conversation topics
            if self._is_echo_content(content, current_topics):
                continue

            all_memories.append({
                **rag_memory,
                'source': 'rag',
                'importance': 0.4  # Lower importance for RAG memories
            })
            seen_content_hashes.add(content_hash)

            # Stop if we have enough memories
            if len(all_memories) >= max_memories:
                break

        # Sort by importance and recency with diversity consideration
        all_memories.sort(key=lambda m: (
            m.get('importance', 0.4),
            m.get('timestamp', 0),
            1 if m.get('source') == 'enhanced' else 0  # Prefer enhanced memories
        ), reverse=True)

        final_memories = all_memories[:max_memories]

        logger.info(f"Retrieved {len([m for m in final_memories if m.get('source') == 'enhanced'])} enhanced + "
                   f"{len([m for m in final_memories if m.get('source') == 'rag'])} RAG memories for group conversation "
                   f"(echo prevention enabled)")
        return final_memories

    def _get_content_hash(self, content: str) -> str:
        """Generate a hash for content deduplication"""
        import hashlib

        # Normalize content for hashing
        normalized = ' '.join(content.lower().split())

        # Create hash of first 100 characters (to catch very similar content)
        content_snippet = normalized[:100]
        return hashlib.md5(content_snippet.encode()).hexdigest()

    def _extract_conversation_topics(self, query: str, conversation_context: List[str]) -> List[str]:
        """Extract key topics from query and recent conversation"""

        topics = []

        # Extract topics from current query
        query_words = [word.lower() for word in query.split() if len(word) > 3]
        topics.extend(query_words)

        # Extract topics from recent conversation context
        for context_msg in conversation_context[-3:]:  # Last 3 messages
            context_words = [word.lower() for word in context_msg.split() if len(word) > 3]
            topics.extend(context_words)

        # Remove duplicates and common words
        common_words = {'that', 'this', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'}

        unique_topics = list(set(topic for topic in topics if topic not in common_words))

        return unique_topics[:10]  # Limit to top 10 topics

    def _is_echo_content(self, content: str, current_topics: List[str]) -> bool:
        """Check if content echoes current conversation topics"""

        if not current_topics:
            return False

        content_lower = content.lower()
        echo_count = 0

        # Check for direct topic matches
        for topic in current_topics:
            if topic in content_lower:
                echo_count += 1

        # Consider it an echo if it matches too many current topics
        echo_threshold = min(3, len(current_topics) // 2)
        return echo_count >= echo_threshold

    def _determine_role(self, memory: Memory) -> str:
        """Determine the role for a memory based on its type and content"""

        if memory.memory_type == MemoryTier.CORE_PERSONALITY:
            return "system"  # Core personality traits
        elif memory.user_id == "system":
            return "assistant"  # Luna's own memories
        else:
            return "user"  # User-related memories

    async def learn_from_conversation(self, messages: List[Dict], user_id: str = None,
                                    context: Dict = None):
        """
        Learn and store important information from conversation
        """

        if not self.memory_learning_enabled:
            return

        try:
            # Extract learnable information from recent messages
            for message in messages[-3:]:  # Look at last 3 messages
                if message.get('role') == 'user':
                    await self._extract_user_info(message, user_id, context)
                elif message.get('role') == 'assistant':
                    await self._extract_luna_info(message, context)

        except Exception as e:
            logger.error(f"Error learning from conversation: {e}")

    async def _extract_user_info(self, message: Dict, user_id: str, context: Dict):
        """Extract and store user information from their messages"""

        content = message.get('content', '').lower()

        # Detect preferences
        preference_patterns = [
            ("i love", "loves"),
            ("i hate", "hates"),
            ("i prefer", "prefers"),
            ("my favorite", "favorite"),
            ("i always", "always"),
            ("i never", "never")
        ]

        for pattern, preference_type in preference_patterns:
            if pattern in content:
                # Extract the object of preference
                start_idx = content.find(pattern) + len(pattern)
                preference_text = content[start_idx:].strip()

                # Store as preference memory
                self.enhanced_memory.store_memory(
                    content=f"User {preference_type} {preference_text}",
                    memory_type=MemoryTier.PREFERENCES,
                    importance_score=0.7,
                    user_id=user_id,
                    context={
                        "preference_type": preference_type,
                        "extracted_from": "conversation",
                        **context
                    }
                )
                break

    async def _extract_luna_info(self, message: Dict, context: Dict):
        """Extract and store Luna's own statements for consistency"""

        content = message.get('content', '').lower()

        # Detect Luna's preferences or statements about herself
        luna_patterns = [
            ("i love", "loves"),
            ("i prefer", "prefers"),
            ("my favorite", "favorite"),
            ("i'm obsessed with", "obsessed with"),
            ("i always", "always"),
            ("i think", "thinks")
        ]

        for pattern, statement_type in luna_patterns:
            if pattern in content:
                # Store Luna's self-statements for consistency
                self.enhanced_memory.store_memory(
                    content=f"Luna {statement_type} {content[content.find(pattern) + len(pattern):].strip()}",
                    memory_type=MemoryTier.PREFERENCES,
                    importance_score=0.8,  # Luna's own statements are important for consistency
                    user_id="luna",
                    context={
                        "statement_type": statement_type,
                        "self_reported": True,
                        **context
                    }
                )
                break

    def format_memories_for_prompt(self, memories: List[Dict], is_dm: bool = False) -> str:
        """
        Format retrieved memories for inclusion in LLM prompt
        """

        if not memories:
            return "None"

        # Group memories by type for better organization
        personality_memories = [m for m in memories if m.get('memory_type') == MemoryTier.CORE_PERSONALITY]
        preference_memories = [m for m in memories if m.get('memory_type') == MemoryTier.PREFERENCES]
        fact_memories = [m for m in memories if m.get('memory_type') == MemoryTier.LONG_TERM_FACTS]
        other_memories = [m for m in memories if m.get('memory_type') not in [MemoryTier.CORE_PERSONALITY, MemoryTier.PREFERENCES, MemoryTier.LONG_TERM_FACTS]]

        formatted_sections = []

        # Core personality (always include if present)
        if personality_memories:
            formatted_sections.append("Core Personality:")
            for memory in personality_memories[:3]:  # Limit to top 3
                formatted_sections.append(f"- {memory['content']}")

        # Preferences (important for consistency)
        if preference_memories:
            formatted_sections.append("\nRelevant Preferences:")
            for memory in preference_memories[:5]:  # Limit to top 5
                formatted_sections.append(f"- {memory['content']}")

        # Important facts
        if fact_memories:
            formatted_sections.append("\nImportant Facts:")
            for memory in fact_memories[:4]:  # Limit to top 4
                formatted_sections.append(f"- {memory['content']}")

        # Other relevant memories
        if other_memories and not is_dm:  # Include broader context for group conversations
            formatted_sections.append("\nOther Relevant Context:")
            for memory in other_memories[:3]:  # Limit to top 3
                role = memory.get('role', 'unknown')
                formatted_sections.append(f"- {role}: {memory['content']}")

        return "\n".join(formatted_sections) if formatted_sections else "None"

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about the memory system"""

        # This would query the database for stats
        return {
            "total_memories": "N/A",  # Would implement actual counting
            "core_personality_traits": "N/A",
            "user_preferences": "N/A",
            "recent_consolidations": "N/A",
            "memory_conflicts_detected": "N/A"
        }
