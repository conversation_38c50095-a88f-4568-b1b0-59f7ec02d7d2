import os
import logging
import re
import discord
import time
import asyncio
import json

# Import necessary components from other modules within the package
from .config import (
    TRANSCRIPT_LOG_CHANNEL_ID, CALL_NOTIFICATION_CHANNEL_ID_STR,
    RAG_CONTEXT_COUNT, RAG_TOP_K, MAX_HISTORY_MESSAGES, LM_STUDIO_MODEL_NAME, OLLAMA_MODEL_NAME,
    DEFAULT_TEMP, TEMP_PARTICIPATION_INCREMENT, MAX_TEMP, MAX_OUTPUT_TOKENS, TOP_P,
    DM_MAX_OUTPUT_TOKENS, DM_TEMP, PROMPT_LOG_CHANNEL_ID,
    get_id_from_alias, get_main_name_by_id, get_main_name_from_alias # Import the nickname functions
)
from .db_logger import log_message_to_db
from .initialization import get_lm_studio_client, get_ollama_client
from .decision import should_respond
from .rag_utils import rewrite_query_for_rag, format_conversation_history_for_prompt
# Import command functions carefully
from .commands import handle_screenshot_command, generate_call_message

# Import necessary components from outside the package
from text_to_speech import StreamingSpeechPlayer
from utils import remove_emojis, format_datetime
# from discord_sink import _find_user_from_identifier # Removed incorrect import
from utils import find_user_from_identifier # Import the refactored helper function
import llm_brain # Import the brain module

logger = logging.getLogger(__name__)

# Helper function for legacy RAG fallback
async def _fallback_to_legacy_rag(rag_query_cleaned, is_dm_channel, user_id, bot):
    """Fallback method for legacy RAG retrieval"""
    from .config import RAG_TOP_K  # Import here to avoid circular imports

    local_rag_retriever = getattr(bot, 'rag_retriever', None)
    if local_rag_retriever and local_rag_retriever.is_ready:
        try:
            if is_dm_channel and hasattr(local_rag_retriever, 'retrieve_personalized'):
                retrieved_docs_full = await local_rag_retriever.retrieve_personalized(
                    rag_query_cleaned, user_id=str(user_id), top_k=8
                )
            else:
                retrieved_docs_full = await local_rag_retriever.retrieve(rag_query_cleaned, top_k=RAG_TOP_K)

            if retrieved_docs_full:
                logger.info(f"Legacy RAG retrieved {len(retrieved_docs_full)} memories")
                return retrieved_docs_full
            return []
        except Exception as e:
            logger.error(f"Error during legacy RAG retrieval: {e}", exc_info=True)
            return []
    else:
        logger.warning("No memory retrieval system available")
        return []

# --- Latency Logging Helper ---
async def log_latency_to_discord(bot, message: str):
    """Sends a latency log message to the designated Discord channel."""
    try:
        # Use PROMPT_LOG_CHANNEL_ID for latency logs as well
        log_channel = bot.get_channel(PROMPT_LOG_CHANNEL_ID)
        if log_channel:
            # Use create_task to avoid blocking
            asyncio.create_task(log_channel.send(f"⏱️ LATENCY: {message}"))
        else:
            logger.warning(f"Latency log channel {PROMPT_LOG_CHANNEL_ID} not found.")
    except Exception as e:
        logger.error(f"Error sending latency log to Discord: {e}", exc_info=True)

# Helper function to get the next item from a blocking iterator, handling StopIteration
def _get_next_chunk_or_none(iterator):
    """Calls next() on the iterator, returning None if StopIteration is raised."""
    try:
        return next(iterator)
    except StopIteration:
        return None
    except Exception as e:
        # Log other potential errors from the iterator itself
        logger.error(f"Error getting next chunk from iterator: {e}", exc_info=True)
        return None # Treat other errors as end-of-stream for safety

# --- Main Message Processing Function ---
async def process_user_message(
    *, # Force keyword arguments
    bot, # Required: bot instance from caller
    text: str, # Required: message content
    user_id: int, # Required: author ID
    conversation_history: list, # Required: list of message dicts for context
    system_prompt: str, # Required: system prompt string
    sink=None, # Optional: DiscordSink instance (indicates voice context)
    force_respond=False,
    display_name=None,
    text_channel=None, # Optional but needed for text output if sink is None
    image_analysis_text=None, # Optional: Text analysis of an image attached to the message
    chat_session = None, # Optional: Stateful chat session from sink
    # --- New parameters for DM handling ---
    dm_target_user: discord.User | discord.Member | None = None,
    dm_topic: str | None = None,
    # --- Latency Timestamps ---
    audio_detected_time: float | None = None, # Time first audio packet was received for this turn
    transcription_end_time: float | None = None, # Time transcription finished for the triggering utterance
    brain_decision_context: str | None = None # Optional: Context from the LLM Brain's decision
):
    """Handles voice or text message processing and response generation, incorporating RAG."""
    process_start_time = time.monotonic() # Start timing this function
    # Ensure conversation_history is a list, even if None was passed
    if conversation_history is None:
        conversation_history = []

    try: # Outer try for the whole function
        # Determine context and essential variables
        is_voice_context = sink is not None
        effective_text_channel = text_channel or (sink.text_channel if sink else None)
        effective_display_name = display_name or (f"User_{user_id}" if not sink else "Unknown") # Simplified fallback
        if sink and hasattr(sink, 'get_display_name'): # Check if sink has the method
             effective_display_name = sink.get_display_name(user_id) # Get name via sink if possible

        logger.info(f"Processing {'voice' if is_voice_context else 'text'} message from {effective_display_name}: {text[:30]}...")
        # Log initial latency if timestamps are available (primarily for voice)
        if audio_detected_time and transcription_end_time:
            time_to_process_start = process_start_time - audio_detected_time
            time_transcribe_to_process = process_start_time - transcription_end_time
            await log_latency_to_discord(bot, f"AudioDetect -> ProcessStart: {time_to_process_start:.4f}s")
            await log_latency_to_discord(bot, f"TranscribeEnd -> ProcessStart: {time_transcribe_to_process:.4f}s")

        # --- DM Generation Logic (Handles DM requests before normal flow) ---
        if dm_target_user and dm_topic:
            logger.info(f"Initiating DM generation for {dm_target_user.name} ({dm_target_user.id}) about: {dm_topic}")
            dm_handled = False # Flag to ensure we return after this block
            ollama_client = get_ollama_client() # Use Ollama for DM generation
            try:
                # Construct a prompt specifically for generating the DM content
                dm_generation_prompt = f"""
                {system_prompt}

                ---
                You need to send a Direct Message (DM) to the user '{dm_target_user.display_name}'.
                The intended topic or reason for this DM is: "{dm_topic}".

                Based on this topic, the recent conversation history below, and your personality, generate a short, natural-sounding DM to send to '{dm_target_user.display_name}'.
                Keep it concise and casual, like a real person would send a quick message. Avoid overly formal language or sounding like a generic bot notification.
                Focus on the topic: "{dm_topic}". Make sure the message makes sense being sent as a DM.

                Recent Conversation History (for context):
                {format_conversation_history_for_prompt(conversation_history)}

                Generate ONLY the DM message content below:
                """

                # Get or create DM chat session for statefulness
                dm_session_key = f"dm_{dm_target_user.id}"
                dm_session = bot.chat_sessions.get(dm_session_key)

                if dm_session is None:
                    logger.info(f"Creating new DM chat session for user {dm_target_user.id}")
                    dm_session = {
                        "id": f"dm_{dm_target_user.id}_{int(time.time())}",
                        "messages": [{"role": "system", "content": system_prompt}]
                    }
                    bot.chat_sessions[dm_session_key] = dm_session

                # Use the Ollama client if available
                if not ollama_client:
                     logger.error(f"Cannot generate DM: Ollama client not initialized.")
                     if effective_text_channel:
                         await effective_text_channel.send(f"❌ Error: Could not generate DM for {dm_target_user.mention}, internal setup missing.")
                     dm_handled = True

                else: # Proceed if Ollama client exists
                    dm_generation_start_time = time.monotonic()
                    logger.info("Sending DM generation request to Ollama...")

                    # Simplified prompt for DM generation
                    simple_dm_prompt = f"You need to send a DM about: '{dm_topic}'. Generate a short, casual message:"

                    # Use the DM session messages and add the new prompt
                    messages = dm_session["messages"].copy()
                    messages.append({"role": "user", "content": simple_dm_prompt})

                    # Make the API call using Ollama
                    response = await asyncio.to_thread(
                        ollama_client.chat.completions.create,
                        model=OLLAMA_MODEL_NAME,
                        messages=messages,
                        temperature=DM_TEMP,
                        max_tokens=DM_MAX_OUTPUT_TOKENS
                    )

                    dm_generation_end_time = time.monotonic()
                    logger.info(f"OLLAMA DM GENERATION API CALL: Completed in {dm_generation_end_time - dm_generation_start_time:.6f}s")

                    if not response.choices or response.choices[0].finish_reason != "stop":
                         finish_reason = response.choices[0].finish_reason if response.choices else "Unknown"
                         logger.warning(f"LLM DM generation failed or was blocked. Finish Reason: {finish_reason}")
                         if effective_text_channel:
                             await effective_text_channel.send(f"⚠️ Tried to DM {dm_target_user.mention} about '{dm_topic}', but couldn't generate content (Reason: {finish_reason}).")
                         dm_handled = True

                    else: # Proceed only if generation was successful
                        dm_content = response.choices[0].message.content.strip()

                        if not dm_content:
                            logger.warning(f"LLM generated empty DM content for topic: {dm_topic}")
                            if effective_text_channel:
                                await effective_text_channel.send(f"⚠️ Tried to DM {dm_target_user.mention} about '{dm_topic}', but couldn't think of what to say!")
                            dm_handled = True

                        else: # Proceed only if content is not empty
                            logger.info(f"Generated DM content for {dm_target_user.name}: {dm_content}")

                            # Update DM session with user prompt and assistant response
                            dm_session["messages"].append({"role": "user", "content": simple_dm_prompt})
                            dm_session["messages"].append({"role": "assistant", "content": dm_content})

                            await dm_target_user.send(dm_content)
                            logger.info(f"Successfully sent generated DM to {dm_target_user.name}")
                            log_message_to_db(
                                user_id=str(bot.user.id), role="assistant_dm",
                                content=f"Sent DM to {dm_target_user.name} ({dm_target_user.id}) about '{dm_topic}': {dm_content}",
                                timestamp=time.time(), channel_type="dm", channel_id=str(dm_target_user.id)
                            )
                            dm_handled = True # Mark DM as handled successfully

            except discord.Forbidden as e:
                logger.warning(f"Failed to send DM to {dm_target_user.name}: Permissions or privacy settings. {e}")
                if effective_text_channel: await effective_text_channel.send(f"⚠️ Couldn't send DM to {dm_target_user.mention} (maybe privacy settings?).")
                dm_handled = True
            except discord.HTTPException as e:
                logger.error(f"Failed to send DM to {dm_target_user.name} due to API error: {e}")
                if effective_text_channel: await effective_text_channel.send(f"❌ Failed to send DM to {dm_target_user.mention} due to a Discord error.")
                dm_handled = True
            except Exception as e:
                logger.error(f"Unexpected error during DM generation/sending for {dm_target_user.name}: {e}", exc_info=True)
                if effective_text_channel: await effective_text_channel.send(f"❌ An unexpected error occurred while trying to generate/send a DM to {dm_target_user.mention}.")
                dm_handled = True
            finally:
                if dm_handled:
                    logger.debug("DM handling block finished, returning.")
                    return # Exit process_user_message if DM was handled (or attempted)

        # --- "Call User" command handling ---
        call_match = re.search(r"luna,? call (\w+)(?: on discord)?", text, re.IGNORECASE)
        # --- "Call User" command logic moved to llm_brain.py ---
        # The brain now decides if a call is needed based on the prompt and executes it.
        # This specific regex check and execution block is removed from processing.py.

        # --- Screenshot command handling ---
        screenshot_patterns = [
            r"take a screenshot", r"capture (?:my|the) screen", r"screenshot",
            r"show me what you see", r"analyze my screen", r"look at my screen",
            r"what(?:'s| is) on my screen"
        ]
        if any(re.search(pattern, text.lower()) for pattern in screenshot_patterns):
            if is_voice_context: # Only allow screenshots if in voice context (sink is available)
                logger.info(f"Screenshot command detected: {text}")
                result = await handle_screenshot_command(sink, text, user_id, effective_display_name, effective_text_channel) # Use function from commands.py
                return result # Return whether screenshot was handled
            else:
                logger.info("Screenshot command ignored in text-only context.")
                if effective_text_channel: await effective_text_channel.send("Sorry, I can only take screenshots when I'm in a voice channel.")
                return False # Indicate not handled

        # --- Decision Phase ---
        recent_raw_history = sorted(conversation_history, key=lambda x: x.get("timestamp", 0))[-MAX_HISTORY_MESSAGES:]
        speaker_turn_history = sink.get_recent_speaker_turns(count=5) if is_voice_context else []
        is_direct_address = "luna" in text.lower()
        should_force_respond = force_respond or is_direct_address
        should_luna_respond = False

        if should_force_respond:
            logger.info(f"Decision: Forcing response due to direct address or flag.")
            should_luna_respond = True
        else:
            is_luna_speaking = sink.is_speaking if is_voice_context else False
            # Get channel_id for stateful decision tracking
            channel_id = None
            if is_voice_context and sink.voice_client and sink.voice_client.channel:
                channel_id = sink.voice_client.channel.id
            elif effective_text_channel:
                channel_id = effective_text_channel.id

            logger.info(f"Checking 'should_respond'. Luna speaking state: {is_luna_speaking}, Channel ID: {channel_id}")
            should_luna_respond = await should_respond( # Use function from decision.py
                text=text,
                current_speaker_id=str(user_id),
                conversation_history=recent_raw_history,
                speaker_turn_history=speaker_turn_history,
                is_currently_speaking=is_luna_speaking,
                channel_id=channel_id,
                confidence_threshold=None  # Use default from config
            )

        if not should_luna_respond:
            logger.info("Decision: Luna will not respond.")
            return False # Indicate not handled, exit early

        # --- Brain Action Check ---
        # If Luna should respond, first check if the brain wants to take a specific action
        logger.info("Consulting LLM Brain for potential actions...")
        brain_prompt = ""
        if is_voice_context:
            # Construct voice prompt for brain
            vc_name = sink.voice_client.channel.name if sink and sink.voice_client and sink.voice_client.channel else "Unknown VC"
            vc_id = sink.voice_client.channel.id if sink and sink.voice_client and sink.voice_client.channel else "Unknown VC ID"
            turn_history = sink.get_recent_speaker_turns(count=5) # Get recent turns for context

            # Use the canonical names from the turn history
            turn_lines = []
            for turn in turn_history:
                # Get the canonical name for this user
                display_name = turn['display_name']
                turn_lines.append(f"{display_name} ({turn['user_id']}): {turn['text']}")

            brain_prompt = f"Conversation turn in voice channel '{vc_name}' ({vc_id}):\n"
            brain_prompt += "\n".join(turn_lines)
            brain_prompt += "\nBased on this conversation turn, what action should I take?"
        elif effective_text_channel:
            # Construct text prompt for brain
            # Handle different channel types (DMChannel doesn't have a name attribute)
            if hasattr(effective_text_channel, 'name'):
                channel_name = effective_text_channel.name
                channel_description = f"'#{channel_name}'"
            else:
                # This is likely a DMChannel
                channel_name = "DM"
                channel_description = "DM"

            channel_id = effective_text_channel.id
            brain_prompt = f"User '{effective_display_name}' ({user_id}) in channel {channel_description} ({channel_id}) said: '{text}'"
            if image_analysis_text:
                brain_prompt += f"\nImage Analysis: {image_analysis_text}"
            brain_prompt += "\nBased on this, what action should I take?"
        else:
            logger.warning("Cannot construct brain prompt: No voice or text channel context.")
            # Proceed without brain action check? Or return error? Let's proceed for now.

        brain_action_details = None
        if brain_prompt:
            try:
                brain_action_details = await llm_brain.process_brain_request(
                    prompt=brain_prompt,
                    bot=bot,
                    sink=sink, # Pass sink for context if needed by brain actions
                    system_prompt=system_prompt, # Pass main system prompt for context
                    effective_text_channel=effective_text_channel
                )
            except Exception as brain_err:
                logger.error(f"Error calling LLM Brain: {brain_err}", exc_info=True)
                brain_action_details = {'action': 'error', 'details': f"Exception during brain call: {brain_err}"}

        # Handle Brain Action Result
        if brain_action_details:
            action_taken = brain_action_details.get('action', 'error')
            logger.info(f"Brain Action Result: {action_taken}, Details: {brain_action_details}")

            # --- Handle Specific Brain Actions ---
            dm_target_identifier_from_brain = None
            dm_topic_from_brain = None

            if action_taken == 'call_user' or action_taken == 'disconnect_user':
                 # These actions are fully executed by the brain function
                 if brain_action_details.get('success', False):
                      logger.info(f"Brain successfully executed action: {action_taken}. Turn processing complete.")
                      return True # Indicate message was handled by the brain action
                 else:
                      logger.warning(f"Brain action '{action_taken}' failed execution. Raw: {brain_action_details.get('raw_decision')}")
                      # Action was attempted but failed, stop further processing.
                      # Brain function should have sent any necessary error message.
                      return False # Indicate message handling failed / stopped

            elif action_taken == 'dm_user':
                 # DM action is only parsed by the brain, execution happens later here.
                 if brain_action_details.get('success', False): # Success here means parsing succeeded
                      logger.info("Brain decided 'DM User'. Storing details for DM generation.")
                      dm_target_identifier_from_brain = brain_action_details.get('target')
                      dm_topic_from_brain = brain_action_details.get('topic')
                      # DO NOT return here. Let processing continue to DM generation phase.
                      # We might want to add the raw decision as context for the main LLM?
                      # brain_decision_context = brain_action_details.get('raw_decision') # Example
                 else:
                      # Should not happen if success is True, but handle defensively
                      logger.error(f"Brain action 'dm_user' indicated success=False unexpectedly. Raw: {brain_action_details.get('raw_decision')}")
                      # Proceed with standard response as fallback.

            elif action_taken == 'no_action':
                logger.info("Brain decided 'No Action'. Proceeding with standard response generation.")
                # Optionally, pass brain's reasoning (raw_decision) as context?
                # brain_decision_context = brain_action_details.get('raw_decision') # Example
            elif action_taken == 'error':
                logger.error(f"Brain processing resulted in an error: {brain_action_details.get('details')}. Raw: {brain_action_details.get('raw_decision')}")
                # Proceed with standard response as a fallback
            else:
                 logger.warning(f"Unknown action returned from brain: {action_taken}. Proceeding with standard response.")

        # --- If brain didn't handle it, proceed with RAG and standard response generation ---
        logger.info("Proceeding with RAG and standard response generation.")

        # --- Response Generation Phase ---
        logger.info("Decision: Luna will respond.")
        lm_studio_client = get_lm_studio_client() # Ensure client is available

        # --- Enhanced Memory Retrieval ---
        rag_context = []

        # Check if this is a DM channel to determine memory retrieval strategy
        is_dm_channel = text_channel and hasattr(text_channel, 'type') and text_channel.type == discord.ChannelType.private

        # Clean the query for memory retrieval
        rag_query_cleaned = re.sub(r"^(?:hey |yo |okay |ok )?luna[,.!?]?\s*", "", text.strip(), flags=re.IGNORECASE)
        if not rag_query_cleaned: rag_query_cleaned = text

        # Prepare conversation context for echo prevention
        conversation_context = []
        if conversation_history:
            # Extract recent message content for context
            for msg in conversation_history[-5:]:  # Last 5 messages
                content = msg.get('content', '').strip()
                if content:
                    conversation_context.append(content)

        # Use enhanced memory integration if available
        memory_integration = getattr(bot, 'memory_integration', None)
        if memory_integration:
            try:
                logger.info(f"Using enhanced memory system for {'DM' if is_dm_channel else 'guild'} conversation")

                # Determine memory count based on channel type
                max_memories = 8 if is_dm_channel else 15

                # Retrieve memories using enhanced system with echo prevention
                retrieved_memories = await memory_integration.retrieve_for_conversation(
                    query=rag_query_cleaned,
                    is_dm=is_dm_channel,
                    user_id=str(user_id),
                    max_memories=max_memories,
                    conversation_context=conversation_context
                )

                if retrieved_memories:
                    rag_context = retrieved_memories
                    logger.info(f"Enhanced memory system retrieved {len(rag_context)} memories "
                               f"({'DM' if is_dm_channel else 'guild'} mode, echo prevention enabled)")

                    # Learn from this conversation automatically
                    if conversation_history:
                        try:
                            learned_memories = memory_integration.enhanced_memory.learn_from_conversation(
                                messages=conversation_history[-3:],  # Learn from recent messages
                                user_id=str(user_id),
                                conversation_type="dm" if is_dm_channel else "voice"
                            )
                            if learned_memories:
                                logger.info(f"Automatically learned {len(learned_memories)} new memories from conversation")
                        except Exception as learn_err:
                            logger.error(f"Error during automatic memory learning: {learn_err}", exc_info=True)
                else:
                    logger.info("Enhanced memory system returned no memories")
                    rag_context = []

            except Exception as e:
                logger.error(f"Error during enhanced memory retrieval: {e}", exc_info=True)
                rag_context = []

                # Fallback to legacy RAG system
                logger.info("Falling back to legacy RAG system")
                rag_context = await _fallback_to_legacy_rag(rag_query_cleaned, is_dm_channel, user_id, bot)
        else:
            # No enhanced memory system available, use legacy RAG
            logger.warning("Enhanced memory system not available, using legacy RAG")
            rag_context = await _fallback_to_legacy_rag(rag_query_cleaned, is_dm_channel, user_id, bot)

        # --- Format RAG Context for Prompt ---
        rag_context_text = "None"
        if rag_context:
            # Use different formatting strategies for DMs vs guild channels
            if is_dm_channel:
                # For DMs, use all retrieved memories (already limited to 8)
                refined_rag_context = rag_context
                logger.info(f"Using all {len(refined_rag_context)} personalized memories for DM")
                formatted_rag_list = ["Your memories with this person:"]
                for doc in refined_rag_context:
                    speaker_role = doc.get('role', 'Unknown')
                    # Add timestamp info if available for better context
                    timestamp = doc.get('timestamp', '')
                    if timestamp:
                        try:
                            import datetime
                            dt = datetime.datetime.fromtimestamp(float(timestamp))
                            time_str = dt.strftime("%b %d")
                            formatted_rag_list.append(f"- ({time_str}) {speaker_role}: {doc.get('content', '')}")
                        except:
                            formatted_rag_list.append(f"- {speaker_role}: {doc.get('content', '')}")
                    else:
                        formatted_rag_list.append(f"- {speaker_role}: {doc.get('content', '')}")
            else:
                # For guild channels, use the original logic
                refined_rag_context = rag_context[:RAG_CONTEXT_COUNT] # Select top N most relevant
                logger.info(f"Refined RAG context from {len(rag_context)} to {len(refined_rag_context)} most relevant entries.")
                formatted_rag_list = ["Relevant Past Interactions (most relevant first):"]
                for doc in refined_rag_context:
                    speaker_role = doc.get('role', 'Unknown')
                    formatted_rag_list.append(f"- {speaker_role}: {doc.get('content', '')}")

            rag_context_text = "\n".join(formatted_rag_list)
            logger.info(f"Formatted {len(refined_rag_context)} refined RAG entries into string.")
        logger.debug(f"RAG Context being used:\n---\n{rag_context_text}\n---")

        # --- Prepare History for Prompt ---
        # Use the same recent_raw_history slice defined earlier
        conversation_for_prompt = []
        for msg in recent_raw_history:
            role = msg.get("role", "user")
            content = msg.get("content", "").strip()
            if content: # Skip empty messages
                # For assistant messages, use "Luna"
                if role == "assistant":
                    speaker_label = "Luna"
                    conversation_for_prompt.append(f"{speaker_label}: {content}")
                else:
                    # For user messages, try to extract the name from the content
                    # The content format should be "Name: message"
                    name_match = re.match(r"^([^:]+):", content)
                    if name_match:
                        # Use the name from the content
                        speaker_label = name_match.group(1).strip()
                        # Extract just the message part
                        message_text = re.sub(r"^[^:]+:\s*", "", content)
                        conversation_for_prompt.append(f"{speaker_label}: {message_text}")
                    else:
                        # If no name in content, try to get canonical name from user ID
                        user_id = msg.get('user_id', 'unknown')
                        # Handle both string and int user_id
                        try:
                            user_id_int = int(user_id) if isinstance(user_id, str) else user_id
                            canonical_name = get_main_name_by_id(user_id_int)
                            if canonical_name:
                                speaker_label = canonical_name
                            else:
                                speaker_label = f"User_{user_id}"
                        except (ValueError, TypeError):
                            # If user_id can't be converted to int, use fallback
                            speaker_label = f"User_{user_id}"
                        conversation_for_prompt.append(f"{speaker_label}: {content}")

        # --- Prepare Final Prompt Parts ---
        # System instruction (passed separately to Vertex AI)
        final_system_prompt = system_prompt # Use the one passed in

        # Construct the user message content, including context
        current_turn_user_content = ""

        # Check if this is a DM channel to determine if we should add context
        is_dm_channel = text_channel and hasattr(text_channel, 'type') and text_channel.type == discord.ChannelType.private

        # For DMs, use simple clean messages for better statefulness
        if is_dm_channel:
            # For DMs, just use the user's message without extra context
            current_turn_user_content = text
            logger.info("DM detected: Using clean message content without RAG/context injection")
        else:
            # For guild channels, add full context as before
            # Add RAG context first if available
            if rag_context_text != "None":
                current_turn_user_content += f"--- Relevant Information (Memory) ---\n{rag_context_text}\n---\n\n"
            # Add image analysis if available
            if image_analysis_text:
                current_turn_user_content += f"--- Image Analysis ---\n{image_analysis_text}\n---\n\n"
            # Add brain decision context if available
            if brain_decision_context:
                 current_turn_user_content += f"--- My Prior Thought/Action Plan ---\n{brain_decision_context}\n---\n\n"
            # Add the actual user message
            current_turn_user_content += f"{effective_display_name}: \"{text}\""

        # --- Prepare History for LM Studio (OpenAI format) ---
        history_messages = []
        # Add system prompt as the first message
        if system_prompt:
            history_messages.append({"role": "system", "content": system_prompt})

        # Add historical messages (excluding the current one)
        for msg in recent_raw_history: # Iterate through the limited history again
             role = msg.get("role")
             content = msg.get("content", "").strip()
             if role and content:
                  openai_role = "assistant" if role == "assistant" else "user"
                  history_messages.append({"role": openai_role, "content": content})

        # --- Generation Config ---
        participation_count = sink.participation_counter.get(user_id, 0) if is_voice_context else 0
        dynamic_temp = min(DEFAULT_TEMP + (TEMP_PARTICIPATION_INCREMENT * participation_count), MAX_TEMP)
        generation_config = {
            "temperature": dynamic_temp,
            "max_tokens": MAX_OUTPUT_TOKENS,
            "top_p": TOP_P,
        }

        # --- Log Full Prompt to Console (if enabled) ---
        if os.environ.get("VERBOSE_PROMPT_LOGGING", "False").lower() == "true":
            try: # Try block for console logging
                print("\n--- LM STUDIO REQUEST ---")
                print(f"Model: {LM_STUDIO_MODEL_NAME}")
                print(f"Context: {'Voice (Streaming)' if is_voice_context and chat_session else ('Text (Stateful)' if chat_session else 'Text (Stateless)')}")
                print("\n--- System Prompt ---")
                print(final_system_prompt)
                print("\n--- History ---")
                for i, msg in enumerate(history_messages):
                    print(f"[{i}] {msg['role']}: {msg['content'][:100]}...")
                print("\n--- Current Turn User Content ---")
                print(current_turn_user_content)
                print("--- END REQUEST ---\n")
            except Exception as console_log_e: # Added except block
                logger.warning(f"Could not display full LM Studio prompt details to console: {console_log_e}")

        # --- Log Simplified Prompt to Discord Channel (if enabled) ---
        prompt_log_channel = bot.get_channel(PROMPT_LOG_CHANNEL_ID)
        if prompt_log_channel and os.environ.get("VERBOSE_PROMPT_LOGGING", "False").lower() == "true":
            try: # Try block for Discord logging
                log_content = ""
                log_title = ""

                # Determine context for title and content
                is_stateful = chat_session is not None
                context_type = ""
                if is_stateful:
                    context_type = "Voice Stream" if is_voice_context else "Text Stateful"
                    log_content = current_turn_user_content # Only log current turn content for stateful
                else: # Stateless fallback
                    context_type = "Text Stateless"
                    # Combine system prompt and history for stateless log
                    log_content = f"System Prompt:\n{final_system_prompt}\n\nHistory & Current Turn:\n"
                    # Format history messages
                    for msg in history_messages:
                        log_content += f"{msg['role']}: {msg['content']}\n"
                    # Add current turn
                    log_content += f"user: {current_turn_user_content}\n"

                log_title = f"**=== LM STUDIO PROMPT ({context_type}) ===**"
                full_log_message = f"{log_title}\n```\n{log_content.strip()}\n```"

                # Use create_task to avoid blocking, handle splitting
                async def send_log_message():
                    try:
                        if len(full_log_message) > 1950: # Adjust limit slightly for safety
                            await prompt_log_channel.send(log_title) # Send title first
                            content_chunks = [log_content[i:i+1900] for i in range(0, len(log_content), 1900)]
                            for chunk in content_chunks:
                                await prompt_log_channel.send(f"```\n{chunk.strip()}\n```")
                        else:
                            await prompt_log_channel.send(full_log_message)
                    except Exception as send_err:
                        logger.error(f"Failed to send prompt log to Discord channel {PROMPT_LOG_CHANNEL_ID}: {send_err}", exc_info=True)

                # Create the task to send the message
                asyncio.create_task(send_log_message())

            except Exception as discord_log_e: # Added except block
                logger.warning(f"Could not log prompt to Discord channel {PROMPT_LOG_CHANNEL_ID}: {discord_log_e}", exc_info=True)
        # --- End Discord Prompt Logging ---


        # --- Execute DM Action if Decided by Brain ---
        logger.info(f"DEBUG: DM variables - target='{dm_target_identifier_from_brain}', topic='{dm_topic_from_brain}'")
        if dm_target_identifier_from_brain and dm_topic_from_brain:
            logger.info(f"Attempting to execute brain-decided DM: Target='{dm_target_identifier_from_brain}', Topic='{dm_topic_from_brain}'")
            dm_handled = False
            target_user_for_dm = None
            try:
                # Resolve the identifier to a user object using the imported helper
                # Note: This helper needs the 'bot' and potentially 'guild' context.
                # We need to pass the necessary context. Assuming 'bot' is available.
                # The helper might need adjustment if it relies heavily on sink state not available here.
                # For now, assume it works with 'bot' and the identifier.
                # We need to instantiate the sink class to call its method, or make the method static/standalone.
                # Let's assume we can call it via the bot object if sink is attached there, or refactor is needed.
                # TEMPORARY WORKAROUND: We will skip resolving for now and assume the identifier is usable,
                # OR we need to properly refactor _find_user_from_identifier.
                # Let's proceed assuming we need to call the main LLM to generate the DM content first.

                # --- Generate DM Content ---
                # Construct a prompt for the main LLM to generate the actual DM message
                # Use the main system prompt for personality, plus specific instructions
                dm_generation_prompt = f"""{final_system_prompt}

---
You need to write a short, casual Direct Message (DM).
The intended recipient is identified as: '{dm_target_identifier_from_brain}' (resolve this to their main name if known).
The reason or topic for this DM is: "{dm_topic_from_brain}".

Based ONLY on this topic and your personality, generate the DM message content.
Keep it concise and natural-sounding. Do NOT add greetings like "Hey [Name]," unless it flows very naturally with the topic.
Focus on conveying the core topic: "{dm_topic_from_brain}".
Generate ONLY the DM message content below:
"""
                # Use Ollama client for DM generation
                ollama_client = get_ollama_client()
                logger.info(f"DEBUG: Ollama client for DM generation: {ollama_client}")
                if ollama_client:
                    logger.info(f"Generating DM content for target '{dm_target_identifier_from_brain}' about '{dm_topic_from_brain}'...")
                    dm_gen_start_time = time.monotonic()

                    # Get or create DM chat session for statefulness
                    dm_session_key = f"dm_{dm_target_identifier_from_brain}"
                    dm_session = bot.chat_sessions.get(dm_session_key)

                    if dm_session is None:
                        logger.info(f"Creating new DM chat session for target {dm_target_identifier_from_brain}")
                        dm_session = {
                            "id": f"dm_{dm_target_identifier_from_brain}_{int(time.time())}",
                            "messages": [{"role": "system", "content": system_prompt}]
                        }
                        bot.chat_sessions[dm_session_key] = dm_session

                    # Simplified prompt for DM generation
                    simple_dm_prompt = f"You need to send a DM about: '{dm_topic_from_brain}'. Generate a short, casual message:"

                    # Use the DM session messages and add the new prompt
                    messages = dm_session["messages"].copy()
                    messages.append({"role": "user", "content": simple_dm_prompt})

                    # Make the API call using Ollama
                    dm_response = await asyncio.to_thread(
                        ollama_client.chat.completions.create,
                        model=OLLAMA_MODEL_NAME,
                        messages=messages,
                        temperature=DM_TEMP,
                        max_tokens=DM_MAX_OUTPUT_TOKENS
                    )

                    dm_gen_end_time = time.monotonic()
                    logger.info(f"DM content generation took {dm_gen_end_time - dm_gen_start_time:.4f}s")

                    if not dm_response.choices or dm_response.choices[0].finish_reason != "stop":
                         finish_reason_dm = dm_response.choices[0].finish_reason if dm_response.choices else "Unknown"
                         logger.warning(f"LLM DM content generation failed or was blocked. Finish Reason: {finish_reason_dm}")
                         if effective_text_channel: await effective_text_channel.send(f"⚠️ Tried to DM '{dm_target_identifier_from_brain}' about '{dm_topic_from_brain}', but couldn't generate content (Reason: {finish_reason_dm}).")
                    else:
                         dm_content_to_send = dm_response.choices[0].message.content.strip()
                         if not dm_content_to_send:
                              logger.warning(f"LLM generated empty DM content for topic: {dm_topic_from_brain}")
                              if effective_text_channel: await effective_text_channel.send(f"⚠️ Tried to DM '{dm_target_identifier_from_brain}' about '{dm_topic_from_brain}', but couldn't think of what to say!")
                         else:
                              # --- Find User and Send DM ---
                              logger.info(f"Attempting to find user '{dm_target_identifier_from_brain}' to send DM...")
                              # Use the refactored helper function from utils.py
                              # Determine the relevant guild context (if any)
                              guild_context = sink.voice_client.guild if sink and sink.voice_client else (effective_text_channel.guild if effective_text_channel else None)
                              target_user_for_dm = await find_user_from_identifier(
                                  identifier=dm_target_identifier_from_brain,
                                  bot=bot,
                                  guild=guild_context # Pass guild if available for name fallback
                              )

                              if target_user_for_dm:
                                   try:
                                        # Update DM session with user prompt and assistant response
                                        dm_session["messages"].append({"role": "user", "content": simple_dm_prompt})
                                        dm_session["messages"].append({"role": "assistant", "content": dm_content_to_send})

                                        await target_user_for_dm.send(dm_content_to_send)
                                        logger.info(f"Successfully sent generated DM to {target_user_for_dm.name} ({target_user_for_dm.id})")
                                        # Log to DB (consider adding a dedicated function)
                                        log_message_to_db(
                                             user_id=str(bot.user.id), role="assistant_dm",
                                             content=f"Sent DM to {target_user_for_dm.name} ({target_user_for_dm.id}) about '{dm_topic_from_brain}': {dm_content_to_send}",
                                             timestamp=time.time(), channel_type="dm", channel_id=str(target_user_for_dm.id)
                                        )
                                        dm_handled = True
                                   except discord.Forbidden:
                                        logger.warning(f"Failed to send DM to {target_user_for_dm.name}: Permissions or privacy settings.")
                                        if effective_text_channel: await effective_text_channel.send(f"⚠️ Couldn't send DM to {target_user_for_dm.mention} (maybe privacy settings?).")
                                        dm_handled = True # Still handled, just failed sending
                                   except discord.HTTPException as e_dm_send:
                                        logger.error(f"Failed to send DM to {target_user_for_dm.name} due to API error: {e_dm_send}")
                                        if effective_text_channel: await effective_text_channel.send(f"❌ Failed to send DM to {target_user_for_dm.mention} due to a Discord error.")
                                        dm_handled = True # Still handled, just failed sending
                              else:
                                   logger.warning(f"Could not find user matching '{dm_target_identifier_from_brain}' to send DM.")
                                   if effective_text_channel: await effective_text_channel.send(f"❓ Brain wanted to DM '{dm_target_identifier_from_brain}', but I couldn't find them.")
                                   dm_handled = True # Handled (finding failed)

                else:
                     logger.error("Cannot generate DM content: Ollama client not available.")
                     if effective_text_channel: await effective_text_channel.send("❌ Cannot generate DM content: Ollama client not available.")
                     dm_handled = True # Handled (generation failed)

            except Exception as e_dm_proc:
                logger.error(f"Unexpected error during DM processing for target '{dm_target_identifier_from_brain}': {e_dm_proc}", exc_info=True)
                if effective_text_channel: await effective_text_channel.send(f"❌ An unexpected error occurred while trying to process the DM for '{dm_target_identifier_from_brain}'.")
                dm_handled = True # Mark as handled even on unexpected error

            if dm_handled:
                logger.info("DM action processed (or failed). Skipping standard response.")
                return True # Indicate turn was handled by DM attempt

        # --- If not DMing, Execute Standard API Call ---
        # (Original code for voice/text responses follows)
        try: # Try block for API calls
            # Case 1: Stateful Voice (Sink and Session exist)
            if sink and chat_session:
                logger.info("Generating STATEFUL VOICE response (Streaming)")
                voice_client = sink.voice_client
                if not voice_client:
                    logger.error("ChatSession provided, but no active voice client found in sink!")
                    return False

                if voice_client.is_playing(): voice_client.stop()
                sink.is_speaking = True
                player = StreamingSpeechPlayer(voice_client, after_play_callback=lambda e: sink._after_play(e, user_id))
                await player.start()
                tts_start_time = time.monotonic()
                await log_latency_to_discord(bot, f"TTS Player Started (Time from ProcessStart: {tts_start_time - process_start_time:.4f}s)")

                full_response_text = ""
                response_start_time = time.monotonic()
                logger.info(f"STATEFUL OLLAMA STREAMING RESPONSE API CALL ({OLLAMA_MODEL_NAME}): Starting...")

                # Prepare messages for the API call
                # Copy the messages from the session and add our new prompt
                messages = []
                if isinstance(chat_session, dict) and "messages" in chat_session:
                    # Make a deep copy of the messages to avoid modifying the original
                    messages = chat_session["messages"].copy()
                    # Log the messages being used for the API call
                    logger.info(f"Using {len(messages)} messages from chat session for Ollama API call")
                    for i, msg in enumerate(messages):
                        logger.info(f"Message {i}: role={msg.get('role')}, content={msg.get('content', '')[:50]}...")
                else:
                    # Create a new message list with system prompt
                    if system_prompt:
                        messages.append({"role": "system", "content": system_prompt})
                        logger.info("No valid chat session found, creating new message list with system prompt")

                # Add the current user message
                messages.append({"role": "user", "content": current_turn_user_content})
                logger.info(f"Added current user message to API call messages. Now has {len(messages)} messages")

                try: # Inner try for stream processing
                    # Get the Ollama client
                    ollama_client = get_ollama_client()

                    # Make the streaming API call
                    response_stream = await asyncio.to_thread(
                        ollama_client.chat.completions.create,
                        model=OLLAMA_MODEL_NAME,
                        messages=messages,
                        temperature=generation_config["temperature"],
                        max_tokens=generation_config["max_tokens"],
                        top_p=generation_config["top_p"],
                        stream=True
                    )

                    first_chunk_received = False

                    # Process the streaming response
                    try:
                        # Iterate through chunks using the OpenAI client's built-in iterator
                        for chunk in response_stream:
                            if not first_chunk_received:
                                first_chunk_time = time.monotonic()
                                ttft = first_chunk_time - response_start_time
                                await log_latency_to_discord(bot, f"Ollama TTFT (Voice Stream): {ttft:.4f}s")
                                first_chunk_received = True

                            if hasattr(chunk, 'choices') and chunk.choices and hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                                chunk_text = chunk.choices[0].delta.content
                                full_response_text += chunk_text
                                await player.add_text(chunk_text) # Feed chunk to TTS player

                            # Yield control briefly, allowing other tasks to run
                            await asyncio.sleep(0.001) # Smaller sleep might improve responsiveness slightly
                    except Exception as e:
                        logger.error(f"Error processing stream chunk: {e}", exc_info=True)

                except Exception as stream_err:
                    logger.error(f"Error during LM Studio stateful stream processing: {stream_err}", exc_info=True)
                    await player.add_text("Sorry, something went wrong while I was thinking.")
                finally:
                     await player.finalize() # Ensure player is always finalized
                     response_end_time = time.monotonic()
                     total_api_duration = response_end_time - response_start_time
                     logger.info(f"STATEFUL OLLAMA STREAMING RESPONSE API CALL & PROCESSING ({OLLAMA_MODEL_NAME}): Completed in {total_api_duration:.6f}s")
                     await log_latency_to_discord(bot, f"Ollama Stateful Voice (Stream Total): {total_api_duration:.4f}s")

                if full_response_text:
                    clean_response = remove_emojis(full_response_text.strip())
                    # Meta-commentary removal (optional)
                    # ... (patterns omitted for brevity) ...
                    if clean_response:
                        logger.info(f"Luna Voice Response (Full Streamed Text): {clean_response}")
                        if hasattr(sink, '_add_to_history'):
                            await sink._add_to_history("assistant", clean_response, bot.user.id, None, "voice", voice_client.channel.id if voice_client else None)
                        # Log to transcript channel
                        log_channel = bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                        if log_channel: asyncio.create_task(log_channel.send(f"**Luna:** {clean_response}"))

                        # Update the chat session with the assistant's response
                        if isinstance(chat_session, dict) and "messages" in chat_session:
                            # Check if the user message is already in the session (avoid duplicates)
                            if not any(msg.get("role") == "user" and msg.get("content") == current_turn_user_content for msg in chat_session["messages"]):
                                chat_session["messages"].append({"role": "user", "content": current_turn_user_content})
                                logger.info(f"Added user message to voice chat session")
                            else:
                                logger.info(f"User message already exists in voice chat session, skipping")

                            # Add the assistant's response
                            chat_session["messages"].append({"role": "assistant", "content": clean_response})
                            logger.info(f"Added assistant response to voice chat session. Session now has {len(chat_session['messages'])} messages.")

                            # Log the first few messages in the session for debugging
                            if len(chat_session["messages"]) > 0:
                                logger.info(f"First message in voice session: role={chat_session['messages'][0].get('role')}, content={chat_session['messages'][0].get('content')[:50]}...")
                            if len(chat_session["messages"]) > 1:
                                logger.info(f"Second message in voice session: role={chat_session['messages'][1].get('role')}, content={chat_session['messages'][1].get('content')[:50]}...")
                    else: logger.warning("Full streamed voice response was empty after cleaning.")
                else: logger.warning("Full streamed voice response was empty.")

                sink.participation_counter[user_id] = sink.participation_counter.get(user_id, 0) + 1
                return True

            # Case 2: Stateful Text (Text Channel and Session exist)
            elif effective_text_channel and chat_session:
                logger.info(f"Generating STATEFUL TEXT response for channel {effective_text_channel.id}")
                response_start_time = time.monotonic()

                # Prepare messages for the API call
                messages = []
                if isinstance(chat_session, dict) and "messages" in chat_session:
                    # Make a deep copy of the messages to avoid modifying the original
                    messages = chat_session["messages"].copy()
                    # Log the messages being used for the API call
                    logger.info(f"Using {len(messages)} messages from chat session for Ollama API call")
                    for i, msg in enumerate(messages):
                        logger.info(f"Message {i}: role={msg.get('role')}, content={msg.get('content', '')[:50]}...")
                else:
                    # Create a new message list with system prompt
                    if system_prompt:
                        messages.append({"role": "system", "content": system_prompt})
                    logger.info(f"Created new messages list with system prompt")

                # Add the current user message
                messages.append({"role": "user", "content": current_turn_user_content})
                logger.info(f"Added user message to messages list. Now has {len(messages)} messages")

                # Get the Ollama client
                ollama_client = get_ollama_client()

                # Make the API call
                response = await asyncio.to_thread(
                    ollama_client.chat.completions.create,
                    model=OLLAMA_MODEL_NAME,
                    messages=messages,
                    temperature=generation_config["temperature"],
                    max_tokens=generation_config["max_tokens"],
                    top_p=generation_config["top_p"]
                )

                response_end_time = time.monotonic()
                total_api_duration = response_end_time - response_start_time
                logger.info(f"STATEFUL OLLAMA TEXT RESPONSE API CALL ({OLLAMA_MODEL_NAME}): Completed in {total_api_duration:.6f}s")
                await log_latency_to_discord(bot, f"Ollama Stateful Text: {total_api_duration:.4f}s")

                response_text = response.choices[0].message.content.strip() if response.choices else ""
                clean_response = remove_emojis(response_text)
                if clean_response:
                    await effective_text_channel.send(clean_response)
                    log_message_to_db(bot.user.id, "assistant", clean_response, time.time(), "text", effective_text_channel.id)
                    logger.info(f"Luna Stateful Text Response (Channel {effective_text_channel.id}): {clean_response}")
                    log_channel = bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                    if log_channel: asyncio.create_task(log_channel.send(f"**Luna:** {clean_response}"))

                    # Update the session with the assistant's response
                    if isinstance(chat_session, dict) and "messages" in chat_session:
                        # Check if the user message is already in the session (avoid duplicates)
                        if not any(msg.get("role") == "user" and msg.get("content") == current_turn_user_content for msg in chat_session["messages"]):
                            chat_session["messages"].append({"role": "user", "content": current_turn_user_content})
                            logger.info(f"Added user message to chat session")
                        else:
                            logger.info(f"User message already exists in chat session, skipping")

                        # Add the assistant's response
                        chat_session["messages"].append({"role": "assistant", "content": clean_response})
                        logger.info(f"Added assistant response to chat session. Session now has {len(chat_session['messages'])} messages.")

                        # Log the first few messages in the session for debugging
                        if len(chat_session["messages"]) > 0:
                            logger.info(f"First message in session: role={chat_session['messages'][0].get('role')}, content={chat_session['messages'][0].get('content')[:50]}...")
                        if len(chat_session["messages"]) > 1:
                            logger.info(f"Second message in session: role={chat_session['messages'][1].get('role')}, content={chat_session['messages'][1].get('content')[:50]}...")
                else:
                    logger.warning("Stateful text response was empty.")
                return True

            # Case 3: Stateless Text (Text Channel exists, Session is None) - Fallback
            elif effective_text_channel:
                logger.warning(f"Generating STATELESS TEXT response (Fallback) for channel {effective_text_channel.id}")
                response_start_time = time.monotonic()

                # Prepare messages for the API call
                messages = []
                # Add system prompt
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                    logger.info("Added system prompt to stateless messages list")

                # Add historical messages
                for msg in history_messages:
                    if msg["role"] != "system":  # Skip system messages as we already added one
                        messages.append(msg)
                logger.info(f"Added {len(history_messages)} historical messages to stateless messages list")

                # Add the current user message (if not already in the messages)
                if not any(msg.get("role") == "user" and msg.get("content") == current_turn_user_content for msg in messages):
                    messages.append({"role": "user", "content": current_turn_user_content})
                    logger.info("Added current user message to stateless messages list")

                # Log the final message list
                logger.info(f"Final stateless messages list has {len(messages)} messages")
                for i, msg in enumerate(messages):
                    logger.info(f"Stateless Message {i}: role={msg.get('role')}, content={msg.get('content', '')[:50]}...")

                # Get the Ollama client
                ollama_client = get_ollama_client()

                # Make the API call
                response = await asyncio.to_thread(
                    ollama_client.chat.completions.create,
                    model=OLLAMA_MODEL_NAME,
                    messages=messages,
                    temperature=generation_config["temperature"],
                    max_tokens=generation_config["max_tokens"],
                    top_p=generation_config["top_p"]
                )

                response_end_time = time.monotonic()
                total_api_duration = response_end_time - response_start_time
                logger.info(f"STATELESS OLLAMA TEXT RESPONSE API CALL ({OLLAMA_MODEL_NAME}): Completed in {total_api_duration:.6f}s")
                await log_latency_to_discord(bot, f"Ollama Stateless Text: {total_api_duration:.4f}s")

                response_text = response.choices[0].message.content.strip() if response.choices else ""
                clean_response = remove_emojis(response_text)
                if clean_response:
                    await effective_text_channel.send(clean_response)
                    log_message_to_db(bot.user.id, "assistant", clean_response, time.time(), "text", effective_text_channel.id)
                    logger.info(f"Luna Stateless Text Response (Channel {effective_text_channel.id}): {clean_response}")
                    log_channel = bot.get_channel(TRANSCRIPT_LOG_CHANNEL_ID)
                    if log_channel: asyncio.create_task(log_channel.send(f"**Luna:** {clean_response}"))
                else:
                    logger.warning("Stateless text response was empty.")
                return True

            # Case 4: No valid context
            else:
                logger.error("Reached response generation block but no valid context (sink/session or text channel) was found.")
                return False

        except Exception as e: # Catch errors during API call/response handling
            logger.error(f"Error during response generation API call: {e}", exc_info=True)
            error_message = "Sorry, I encountered an error while trying to respond."
            if sink: await sink.play_error_message(error_message)
            elif effective_text_channel: await effective_text_channel.send(error_message)
            return False # Indicate failure

    except Exception as e: # Catch errors in the outer function scope
         logger.error(f"Outer processing error in process_user_message: {e}", exc_info=True)
         try: # Attempt to report error
             final_effective_text_channel = text_channel or (sink.text_channel if sink else None)
             if final_effective_text_channel: await final_effective_text_channel.send("😵 Uh oh, something went wrong internally.")
         except Exception as report_err: logger.error(f"Failed to send outer error message: {report_err}")
         return False # Indicate failure