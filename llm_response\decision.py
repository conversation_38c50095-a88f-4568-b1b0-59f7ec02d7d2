import logging
import time
import asyncio
import re
from openai import OpenAI # For client creation and types
from openai.types.chat import Chat<PERSON>ompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice

# Import necessary components from other modules within the package
from .config import DECISION_MODEL, DECISION_TEMP, DECISION_MAX_TOKENS, DECISION_CONFIDENCE_THRESHOLD
from .initialization import create_lm_studio_client # To get the client for the decision model

logger = logging.getLogger(__name__)

# Global dictionary to store decision context for each channel
# Format: {channel_id: {"last_decision": bool, "conversation_state": str}}
decision_sessions = {}

async def safe_model_call(client, model, messages, temperature=0.7, max_tokens=50, stream=False):
    """Safe wrapper for model calls with error handling"""
    try:
        # Use asyncio.to_thread for potentially blocking synchronous SDK calls
        completion = await asyncio.to_thread(
            client.chat.completions.create,
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=stream
        )
        return completion
    except Exception as e:
        logger.error(f"Model call error ({model}): {e}", exc_info=True)
        # Create a fallback response object to avoid downstream errors
        fallback_msg = ChatCompletionMessage(role="assistant", content="NO 100%|Error occurred") # Default to NO with high confidence on error
        fallback_choice = Choice(index=0, message=fallback_msg, finish_reason="error")
        # Ensure the fallback object matches the expected structure
        return ChatCompletion(
            id="error_fallback",
            choices=[fallback_choice],
            created=int(time.time()),
            model=model, # Indicate which model failed
            object="chat.completion",
            system_fingerprint=None, # Add required fields if needed by your usage
            usage=None
        )


async def should_respond(text: str, current_speaker_id: str, conversation_history: list, speaker_turn_history: list, is_currently_speaking: bool, channel_id=None, confidence_threshold=None):
    """LLM-powered decision making, focusing on multi-speaker dynamics and avoiding self-interruption.

    Now with stateful conversation tracking to maintain context across messages and confidence scoring.

    Args:
        text: The message text to analyze
        current_speaker_id: ID of the current speaker
        conversation_history: List of recent conversation messages
        speaker_turn_history: List of recent speaker turns
        is_currently_speaking: Whether Luna is currently speaking
        channel_id: Optional channel ID for stateful tracking
        confidence_threshold: Optional override for the confidence threshold (default: DECISION_CONFIDENCE_THRESHOLD from config)
    """
    text_lower = text.lower()
    current_speaker_label = f"User_{current_speaker_id}" # Or fetch display name if available

    # Get or create session for this channel
    session = None
    if channel_id:
        session = decision_sessions.get(channel_id)
        if not session:
            # Initialize a new session
            session = {
                "last_decision": False,
                "conversation_state": "INITIAL"  # Track conversation state: INITIAL, ACTIVE, IDLE
            }
            decision_sessions[channel_id] = session

    # --- Fast Path (Only for direct mentions) ---
    # Only keep the direct mention check as a fast path
    if "luna" in text_lower:
        logger.info("Should respond (Fast Path): Direct mention of Luna.")
        if session:
            session["last_decision"] = True
            session["conversation_state"] = "ACTIVE"
        return True

    # For all other cases, we'll rely on the sophisticated LLM analysis

    # --- Pre-LLM Heuristic: Only track conversation state ---
    if len(speaker_turn_history) >= 2:
        last_speaker = speaker_turn_history[-1].get('display_name', 'Unknown') # Use display_name
        second_last_speaker = speaker_turn_history[-2].get('display_name', 'Unknown') # Use display_name

        # Check if Luna was one of the last two speakers
        luna_in_recent_conversation = last_speaker == "Luna" or second_last_speaker == "Luna"

        # If Luna was one of the last two speakers, we should consider this part of an active conversation
        if luna_in_recent_conversation:
            logger.info("Luna was one of the last two speakers - considering this an active conversation")
            if session:
                session["conversation_state"] = "ACTIVE"

        # We'll let the LLM decide for all other cases

    # --- Note if Luna is speaking (but let the LLM decide) ---
    if is_currently_speaking:
        logger.info("Luna is currently speaking - will let LLM decide if this warrants interruption")

    # --- Gather Context for LLM Evaluation ---
    # Check if Luna was one of the recent speakers
    luna_in_recent_conversation = False
    luna_spoke_last = False

    if speaker_turn_history:
        # Check if Luna was the last speaker
        luna_spoke_last = speaker_turn_history[-1].get('display_name') == "Luna"

        # Check if Luna was in recent conversation
        for turn in speaker_turn_history[-3:] if len(speaker_turn_history) >= 3 else speaker_turn_history:
            if turn.get('display_name') == "Luna":
                luna_in_recent_conversation = True
                break

    # Update session state based on Luna's participation
    if luna_in_recent_conversation and session:
        logger.info("Luna was in the recent conversation - marking as active")
        session["conversation_state"] = "ACTIVE"

    # Get Luna's last message if available (for context)
    luna_last_message = None
    for msg in reversed(conversation_history):
        if msg.get("role") == "assistant":
            luna_last_message = msg.get("content", "").lower()
            break

    # Log useful context for debugging
    if luna_spoke_last:
        logger.info("Luna was the last speaker")

    if luna_last_message:
        logger.info(f"Luna's last message: '{luna_last_message[:50]}...'")

    # Let the LLM make the decision with all this context

    # Format speaker history for the prompt
    speaker_flow_str = " -> ".join([turn.get('display_name', 'Unknown') for turn in speaker_turn_history]) if speaker_turn_history else "No recent turns" # Use 'display_name' key

    # Format content history for the prompt - prioritize messages where Luna was involved
    recent_messages = []
    luna_involved_messages = []

    for msg in conversation_history:
        if msg.get("role") == "assistant" or "luna" in msg.get("content", "").lower():
            luna_involved_messages.append(msg)
        recent_messages.append(msg)

    # Prioritize messages where Luna was involved, but include recent context
    context_messages = luna_involved_messages[-5:] if luna_involved_messages else []
    if len(context_messages) < 5:
        # Add more recent messages if we don't have enough Luna-involved messages
        additional_needed = 5 - len(context_messages)
        context_messages.extend(recent_messages[-additional_needed:])

    # Remove duplicates while preserving order
    seen = set()
    context_messages = [msg for msg in context_messages if not (msg.get("content") in seen or seen.add(msg.get("content")))]

    # Format the content history
    content_history_str = "\n".join([f"{msg.get('role', 'user')}: {msg.get('content', '')}" for msg in context_messages])

    # Add conversation state context if available
    conversation_state_context = ""
    if session:
        conversation_state = session.get("conversation_state", "UNKNOWN")
        last_decision = "YES" if session.get("last_decision") else "NO"
        conversation_state_context = f"Conversation State: {conversation_state} | Last Decision: {last_decision}"

    # **SOPHISTICATED CONVERSATION ANALYSIS PROMPT**
    prompt = f"""You are a conversation analyst determining if Luna should respond to a message in a Discord voice chat.

    === CONVERSATION CONTEXT ===
    Recent speaker flow: {speaker_flow_str}
    Current speaker: {current_speaker_label}
    Luna's current state: {'SPEAKING' if is_currently_speaking else 'IDLE'}
    {conversation_state_context}

    === RECENT CONVERSATION HISTORY ===
    {content_history_str}

    === CURRENT MESSAGE ===
    {current_speaker_label}: "{text}"

    === CONVERSATION DYNAMICS ANALYSIS ===
    1. Analyze if this message is part of an ongoing conversation thread involving Luna
    2. Determine if the message is a direct or indirect response to something Luna said
    3. Consider if the message contains pronouns ('you', 'your') that likely refer to Luna
    4. Evaluate if the message continues a topic or theme that Luna introduced
    5. Check if the message starts with response indicators ('yeah', 'yes', 'no', 'right', etc.)
    6. Assess if the message is seeking Luna's input even without explicitly naming her
    7. Determine if the message is clearly directed at someone other than Luna

    === RESPONSE CRITERIA ===
    Luna SHOULD respond if:
    • Her name is directly mentioned
    • The message is clearly continuing a conversation with her
    • The message references something she said previously
    • The message contains 'you' or 'your' that contextually refers to Luna
    • The message is a question that isn't clearly directed at someone else
    • The message seeks an opinion or input that Luna could reasonably provide
    • The message continues a theme or topic that Luna introduced

    Luna should NOT respond if:
    • The message is clearly part of a human-to-human exchange not involving Luna
    • The message is directed at a specific person other than Luna
    • Luna is currently speaking and the message isn't a direct interruption
    • The message is ambient conversation not seeking Luna's input

    === IMPORTANT INSTRUCTIONS ===
    • Analyze the FULL conversation context, not just the current message
    • Pay special attention to conversational turn-taking patterns
    • Consider the natural flow of human conversation
    • Be sensitive to implicit references to Luna even without her name
    • Recognize when someone is responding to Luna's previous statements
    • Understand that 'you' often refers to Luna in this context

    === YOUR RESPONSE ===
    Respond with "YES" or "NO" followed by a confidence percentage (e.g., "YES 85%" or "NO 60%").
    The confidence percentage should reflect how certain you are about your decision.

    Examples:
    - "YES 95%" (very confident Luna should respond)
    - "YES 75%" (moderately confident Luna should respond)
    - "NO 90%" (very confident Luna should NOT respond)
    - "NO 65%" (moderately confident Luna should NOT respond)

    Keep your reasoning 10 words or less.
    You'll be getting raw transcriptions that show up as follows:
    "Name: Message" or "Gavin: Message" or "Tachi: Message", these are the names of the speakers.
    You will need to analyze the conversation flow and determine if Luna should respond based on the context provided CLOSELY, and mimic that of a humans decision making process to keep a natural flow of conversation.
    """

    try:
        # Use LM Studio client for decision making
        lm_studio_client = create_lm_studio_client() # Get the client instance

        messages = [
            {"role": "system", "content": "You are an expert conversation analyst with deep understanding of human dialogue patterns, turn-taking dynamics, and implicit references. Your task is to determine when an AI assistant named Luna should participate in a conversation based on subtle conversational cues and context. You must respond with 'YES' or 'NO' followed by a confidence percentage (e.g., 'YES 85%' or 'NO 60%')."},
            {"role": "user", "content": prompt}
        ]

        # Log decision LLM call timing
        decision_start_time = time.monotonic()
        response = await safe_model_call(
            client=lm_studio_client,
            model=DECISION_MODEL,
            messages=messages,
            temperature=DECISION_TEMP, # Use temp from config
            max_tokens=DECISION_MAX_TOKENS # Use max_tokens from config
        )
        decision_end_time = time.monotonic()
        logger.info(f"LM STUDIO DECISION API CALL ({DECISION_MODEL}): Completed in {decision_end_time - decision_start_time:.6f}s")

        # Check if the response object indicates an error (from safe_model_call fallback)
        if not response or not response.choices or response.choices[0].finish_reason == "error":
             logger.error(f"Decision LLM call failed or returned error structure. Falling back.")
             # Safer fallback: Only respond if explicitly mentioned on error
             return "luna" in text_lower

        # Proceed with normal response parsing
        decision_text = response.choices[0].message.content.strip()

        # Log the raw decision received from the LLM
        logger.info(f"LLM Decision Raw Output ('{DECISION_MODEL}'): '{decision_text}'")

        # Parse the decision and confidence
        decision_match = re.match(r'(YES|NO)\s+(\d+)%?', decision_text.upper())

        if decision_match:
            decision = decision_match.group(1)
            confidence = int(decision_match.group(2))
            logger.info(f"Parsed Decision: {decision}, Confidence: {confidence}%")
        else:
            # Fallback for unexpected format
            logger.warning(f"Could not parse decision with confidence from: '{decision_text}'")
            # Default to simple YES/NO parsing as fallback
            decision = "YES" if "YES" in decision_text.upper() else "NO"
            confidence = 100 if "YES" in decision_text.upper() else 0  # Default confidence
            logger.info(f"Using fallback decision: {decision}, Default confidence: {confidence}%")

        # Use provided threshold or fall back to config value
        current_threshold = confidence_threshold if confidence_threshold is not None else DECISION_CONFIDENCE_THRESHOLD

        # Determine if Luna should respond based on decision and confidence threshold
        should_luna_respond = (decision == "YES" and confidence >= current_threshold)

        # Log the confidence-based decision
        if decision == "YES":
            if confidence >= current_threshold:
                logger.info(f"Luna will respond (Decision: YES, Confidence: {confidence}% ≥ Threshold: {current_threshold}%)")
            else:
                logger.info(f"Luna will NOT respond despite YES decision (Confidence: {confidence}% < Threshold: {current_threshold}%)")
        else:
            logger.info(f"Luna will NOT respond (Decision: NO, Confidence: {confidence}%)")

        # Update session with the decision
        if session:
            session["last_decision"] = should_luna_respond
            if should_luna_respond:
                session["conversation_state"] = "ACTIVE"
            elif session["conversation_state"] == "ACTIVE":
                # Only transition from ACTIVE to IDLE if we've been active
                session["conversation_state"] = "IDLE"

        return should_luna_respond

    except Exception as e:
       logger.error(f"Unexpected error in should_respond LLM logic: {e}", exc_info=True)
       # Safer fallback: Only respond if explicitly mentioned on error
       return "luna" in text_lower