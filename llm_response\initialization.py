import os
import logging
from openai import OpenAI
import httpx
import backoff

# Import constants from the config module
from .config import (
    LM_STUDIO_URL,
    LM_STUDIO_API_KEY,
    LM_STUDIO_MODEL_NAME,
    OLLAMA_MODEL_NAME,
    KOKORO_BASE_URL
)

logger = logging.getLogger(__name__)

# Global variables for clients
_lm_studio_client = None
_ollama_client = None

def initialize_lm_studio():
    """Initializes the LM Studio client. Must be called after loading env vars."""
    global _lm_studio_client # Declare intent to modify the module-level variable

    # Check if already initialized
    if _lm_studio_client is not None:
        logger.info("LM Studio client already initialized.")
        return _lm_studio_client

    logger.info("Attempting to initialize LM Studio client...")
    try:
        # Create the client
        _lm_studio_client = create_lm_studio_client()
        logger.info(f"LM Studio client initialized successfully for URL: {LM_STUDIO_URL}")

        # Test the connection with a simple request
        response = _lm_studio_client.models.list()
        logger.info(f"LM Studio connection test successful. Available models: {[model.id for model in response.data]}")

        return _lm_studio_client # Return the client
    except Exception as e:
        logger.error(f"CRITICAL: Failed to initialize LM Studio client: {e}", exc_info=True)
        _lm_studio_client = None # Ensure client is None on failure
        raise RuntimeError("LM Studio initialization failed. Cannot continue.") from e # Re-raise the exception

def get_lm_studio_client():
    """Returns the initialized LM Studio client instance."""
    if _lm_studio_client is None:
        logger.warning("LM Studio client accessed before initialization!")
        return initialize_lm_studio() # Attempt initialization if not done
    return _lm_studio_client

def initialize_ollama():
    """Initializes the Ollama client. Must be called after loading env vars."""
    global _ollama_client

    # Check if already initialized
    if _ollama_client is not None:
        logger.info("Ollama client already initialized.")
        return _ollama_client

    logger.info("Attempting to initialize Ollama client...")
    try:
        # Create the Ollama client (using OpenAI client with Ollama endpoint)
        ollama_url = os.environ.get("OLLAMA_URL", "http://127.0.0.1:11434/v1")
        _ollama_client = OpenAI(
            api_key="ollama", # Ollama doesn't require a real API key
            base_url=ollama_url
        )
        logger.info(f"Ollama client initialized successfully for URL: {ollama_url}")
        return _ollama_client
    except Exception as e:
        logger.error(f"Failed to initialize Ollama client: {e}", exc_info=True)
        _ollama_client = None
        return None

def get_ollama_client():
    """Returns the initialized Ollama client instance."""
    if _ollama_client is None:
        logger.warning("Ollama client accessed before initialization!")
        return initialize_ollama() # Attempt initialization if not done
    return _ollama_client

@backoff.on_exception(
    backoff.expo,
    (httpx.TimeoutException, httpx.ConnectError, httpx.ReadTimeout),
    max_tries=3,
    jitter=backoff.full_jitter
)
def create_lm_studio_client():
    """Create and return an OpenAI-compatible client for LM Studio"""
    logger.debug(f"Creating LM Studio client for URL: {LM_STUDIO_URL}")
    return OpenAI(
        api_key=LM_STUDIO_API_KEY,
        base_url=LM_STUDIO_URL
    )

def create_kokoro_client():
    """Create and return an OpenAI-compatible client for Kokoro."""
    logger.debug(f"Creating Kokoro client for URL: {KOKORO_BASE_URL}")
    return OpenAI(
        base_url=KOKORO_BASE_URL,
        api_key="not-needed" # API key is not needed for Kokoro
    )

# --- Initialize clients upon module load (optional, or can be called explicitly) ---
# It might be better to initialize these lazily or explicitly in main.py after env vars are loaded.
# For now, we provide the creation functions.

# Example of explicit initialization call (usually done in main.py)
if __name__ == '__main__':
    logging.basicConfig(level=logging.DEBUG)
    # Load environment variables (replace with your actual loading mechanism)
    # from dotenv import load_dotenv
    # load_dotenv()
    print("Attempting to initialize LM Studio client...")
    try:
        client = initialize_lm_studio()
        print(f"LM Studio Initialized: {client is not None}")
    except Exception as e:
        print(f"LM Studio initialization failed: {e}")

    print("\nAttempting to initialize Ollama client...")
    try:
        ollama_client = initialize_ollama()
        print(f"Ollama Client Initialized: {ollama_client is not None}")
    except Exception as e:
        print(f"Ollama Client initialization failed: {e}")

    print("\nAttempting to create Kokoro client...")
    try:
        k_client = create_kokoro_client()
        print(f"Kokoro Client Created: {k_client is not None}")
    except Exception as e:
        print(f"Kokoro Client Creation failed: {e}")