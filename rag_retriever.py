import sqlite3
import numpy as np
import faiss
import time
import logging
import os
import torch # <-- Import torch
from sentence_transformers import SentenceTransformer
import asyncio

logger = logging.getLogger(__name__)

class RAGRetriever:
    # Determine device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"RAGRetriever using device: {device}")

    # Shared model across instances - Using a potentially better model and specifying device
    # model = SentenceTransformer('all-MiniLM-L6-v2') # Old model
    try:
        model = SentenceTransformer('all-mpnet-base-v2', device=device)
        logger.info(f"SentenceTransformer model 'all-mpnet-base-v2' loaded successfully onto {device}.")
    except Exception as model_load_err:
        logger.error(f"Failed to load SentenceTransformer model onto {device}: {model_load_err}", exc_info=True)
        logger.warning("Falling back to CPU for SentenceTransformer.")
        device = 'cpu' # Fallback device
        try:
             model = SentenceTransformer('all-mpnet-base-v2', device=device)
             logger.info("SentenceTransformer model loaded successfully onto CPU (fallback).")
        except Exception as fallback_load_err:
             logger.critical("Failed to load SentenceTransformer model even on CPU. RAG will not function.", exc_info=True)
             model = None # Critical failure

    _instance = None  # Singleton pattern

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super().__new__(cls)
            # Initialize instance-specific attributes here if they shouldn't be reset by __init__
            cls._instance.index = None
            cls._instance.db_ids = [] # Store corresponding DB IDs
            cls._instance.last_db_update_time = 0 # Track last known DB update time for index freshness
            cls._instance._initialized = False
        return cls._instance

    # INCREASED max_entries default significantly
    def __init__(self, db_path="conversation_logs.db", index_path="faiss_index.idx", max_entries=30000):
        # Check if already initialized to prevent re-running logic in singleton
        if self._initialized:
            return

        self.db_path = db_path
        self.index_path = index_path
        # Ensure max_entries is stored from the potentially updated default or passed value
        self.max_entries = max_entries
        logger.info(f"RAGRetriever initialized with max_entries={self.max_entries}")

        # Attempt to load the persisted index
        self._load_index()

        self._initialized = True
        logger.info(f"RAGRetriever initialized. Index loaded: {self.index is not None}. DB IDs count: {len(self.db_ids)}")


    def _load_index(self):
        """Loads the index from disk if it exists."""
        if os.path.exists(self.index_path):
            try:
                start_time = time.time()
                logger.info(f"Loading RAG index from {self.index_path}...")
                self.index = faiss.read_index(self.index_path)
                # We need to reconstruct db_ids mapping. This is a limitation without storing it separately.
                # For now, we assume the loaded index corresponds to the last build state.
                # A more robust solution would store metadata (like db_ids and last_db_update_time) alongside the index.
                # Let's fetch the IDs corresponding to the current index size from the DB on load.
                # This assumes the index was built with the latest `max_entries` from the DB.
                if self.index.ntotal > 0:
                     self._fetch_ids_for_loaded_index(self.index.ntotal)
                     # Try to get the last modification time of the index file as a proxy
                     self.last_db_update_time = os.path.getmtime(self.index_path)


                load_duration = time.time() - start_time
                logger.info(f"RAG index loaded successfully in {load_duration:.2f}s with {self.index.ntotal} entries.")
            except Exception as e:
                logger.error(f"Error loading RAG index from {self.index_path}: {e}", exc_info=True)
                self.index = None # Ensure index is None if loading failed
                self.db_ids = []
                self.last_db_update_time = 0
        else:
            logger.info(f"No persisted RAG index found at {self.index_path}. Will build on first retrieval or check.")
            self.index = None
            self.db_ids = []
            self.last_db_update_time = 0

    def _fetch_ids_for_loaded_index(self, num_entries):
        """Fetches the DB IDs corresponding to the most recent entries, assuming the loaded index represents them."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute(
                "SELECT id FROM interactions ORDER BY timestamp DESC LIMIT ?",
                (num_entries,)
            )
            # Fetch IDs and reverse to match the order they were likely added during build
            self.db_ids = [row[0] for row in cursor.fetchall()][::-1]
            conn.close()
            logger.info(f"Fetched {len(self.db_ids)} DB IDs corresponding to loaded index.")
        except Exception as e:
            logger.error(f"Error fetching DB IDs for loaded index: {e}", exc_info=True)
            self.db_ids = [] # Reset on error


    @property
    def is_ready(self):
        """Check if the index exists and has entries."""
        return self.index is not None and self.index.ntotal > 0

    def _get_last_db_update_time(self):
        """Gets the timestamp of the most recent interaction in the database."""
        try:
            conn = sqlite3.connect(self.db_path)
            # Use COALESCE to handle empty table case, returning 0
            last_update = conn.execute("SELECT COALESCE(MAX(timestamp), 0) FROM interactions").fetchone()[0]
            conn.close()
            # Convert timestamp if needed (assuming it's stored as Unix timestamp)
            return float(last_update) if last_update else 0
        except Exception as e:
            logger.error(f"Error getting last DB update time: {e}", exc_info=True)
            return 0 # Return 0 on error

    def needs_rebuild(self):
        """Checks if the database has newer entries than the current index."""
        current_db_update_time = self._get_last_db_update_time()
        needs_update = current_db_update_time > self.last_db_update_time
        if needs_update:
             logger.info(f"Index rebuild needed. DB updated at {current_db_update_time}, index last updated at {self.last_db_update_time}")
        else:
             logger.debug(f"Index is up-to-date. DB updated at {current_db_update_time}, index last updated at {self.last_db_update_time}")
        return needs_update

    def build_index(self):
        """Builds or updates the FAISS index and persists it."""
        try:
            start_time = time.time()
            logger.info("Building/Updating RAG index...")

            conn = sqlite3.connect(self.db_path)
            # Fetch the most recent entries for indexing
            cursor = conn.execute(
                "SELECT id, content FROM interactions ORDER BY timestamp DESC LIMIT ?",
                (self.max_entries,)
            )
            # Fetch all results and reverse them to maintain chronological order for indexing
            rows = cursor.fetchall()
            rows.reverse() # Oldest of the batch first
            conn.close()

            if not rows:
                logger.warning("No messages found for RAG indexing")
                # If index exists, maybe clear it? Or leave as is? Let's leave it for now.
                # Optionally clear the persisted file if no data:
                # if os.path.exists(self.index_path): os.remove(self.index_path)
                return

            current_ids = [row[0] for row in rows]
            messages = [row[1] for row in rows]

            logger.info(f"Encoding {len(messages)} messages for RAG index...")
            embeddings = self.model.encode(messages, convert_to_numpy=True, show_progress_bar=False) # Consider adding progress bar for long builds
            dimension = embeddings.shape[1]

            # Create IndexIDMap wrapping IndexFlatL2
            index = faiss.IndexFlatL2(dimension)
            self.index = faiss.IndexIDMap(index)
            self.index.add_with_ids(np.array(embeddings, dtype=np.float32), np.array(current_ids, dtype=np.int64))
            self.db_ids = current_ids # Update the stored IDs

            # Persist the index
            faiss.write_index(self.index, self.index_path)
            logger.info(f"RAG index saved to {self.index_path}")

            # Update the last modified time using the latest timestamp from the DB
            self.last_db_update_time = self._get_last_db_update_time() # Or use time.time() if preferred

            build_duration = time.time() - start_time
            logger.info(f"RAG index built successfully in {build_duration:.2f}s with {self.index.ntotal} entries.")

        except Exception as e:
            logger.error(f"Error building RAG index: {e}", exc_info=True)
            # Don't update last_modified time on error to ensure rebuild is triggered next time

    async def retrieve(self, query, top_k=5):
        """Retrieves relevant message IDs using the index, then fetches full context."""
        if not self.is_ready:
            logger.warning("RAG index not ready or empty, attempting build...")
            # Try building synchronously if called and not ready (consider if async build is better)
            self.build_index()
            if not self.is_ready:
                 logger.error("Failed to build index. Returning empty results.")
                 return []

        try:
            start_time = time.time()
            # Encode query in a thread
            query_embedding = await asyncio.to_thread(
                self.model.encode, [query], convert_to_numpy=True
            )

            # Search in a thread
            def _search():
                distances, retrieved_ids = self.index.search(np.array(query_embedding, dtype=np.float32), top_k)
                # Filter out potential -1 results if query embedding doesn't match anything or k > ntotal
                valid_ids = [int(id_val) for id_val in retrieved_ids[0] if id_val != -1]
                return valid_ids

            retrieved_db_ids = await asyncio.to_thread(_search)

            if not retrieved_db_ids:
                logger.debug("RAG retrieval found no matching IDs.")
                return []

            # Fetch full message details from DB based on retrieved IDs
            # This part could also be run in a thread if DB access is slow
            def _fetch_details():
                try:
                    conn = sqlite3.connect(self.db_path)
                    # Use placeholders to avoid SQL injection
                    placeholders = ','.join('?' for _ in retrieved_db_ids)
                    # Corrected query to use 'user_id' and 'role' instead of 'actor'
                    query_str = f"SELECT id, user_id, role, content, timestamp FROM interactions WHERE id IN ({placeholders}) ORDER BY timestamp DESC"
                    cursor = conn.execute(query_str, retrieved_db_ids)
                    results = cursor.fetchall()
                    conn.close()
                    # Return results as dictionaries for easier use, mapping role/user_id appropriately
                    # We'll use 'role' primarily, maybe add user_id if needed later
                    return [{"id": r[0], "user_id": r[1], "role": r[2], "content": r[3], "timestamp": r[4]} for r in results]
                except Exception as e:
                    logger.error(f"Error fetching details from DB: {e}", exc_info=True)
                    return [] # Return empty list on DB error


            retrieved_messages = await asyncio.to_thread(_fetch_details)

            retrieval_duration = time.time() - start_time
            logger.info(f"RAG retrieval completed in {retrieval_duration:.2f}s, found {len(retrieved_messages)} results.")
            return retrieved_messages

        except Exception as e:
            logger.error(f"Error during RAG retrieval: {e}", exc_info=True)
            return []

    async def retrieve_personalized(self, query, user_id, top_k=8):
        """Retrieves memories specifically related to a user for personalized conversations."""
        if not self.is_ready:
            logger.warning("RAG index not ready for personalized retrieval, attempting build...")
            self.build_index()
            if not self.is_ready:
                logger.error("Failed to build index. Returning empty results.")
                return []

        try:
            start_time = time.time()

            # First, get a larger set of semantically relevant memories
            semantic_top_k = min(top_k * 4, 50)  # Get 4x more for filtering

            # Encode query in a thread
            query_embedding = await asyncio.to_thread(
                self.model.encode, [query], convert_to_numpy=True
            )

            # Search in a thread
            def _search():
                distances, retrieved_ids = self.index.search(np.array(query_embedding, dtype=np.float32), semantic_top_k)
                valid_ids = [int(id_val) for id_val in retrieved_ids[0] if id_val != -1]
                return valid_ids

            retrieved_db_ids = await asyncio.to_thread(_search)

            if not retrieved_db_ids:
                logger.debug("No semantic matches found for personalized retrieval.")
                return []

            # Fetch details and filter by user involvement
            def _fetch_and_filter():
                try:
                    conn = sqlite3.connect(self.db_path)

                    # Get all semantically relevant messages
                    placeholders = ','.join('?' for _ in retrieved_db_ids)
                    query_sql = f"SELECT id, user_id, role, content, timestamp, channel_type FROM interactions WHERE id IN ({placeholders}) ORDER BY timestamp DESC"
                    cursor = conn.execute(query_sql, retrieved_db_ids)
                    all_results = cursor.fetchall()

                    # Filter for user-relevant memories
                    user_relevant = []
                    for r in all_results:
                        # Include if:
                        # 1. User was directly involved (sent the message)
                        # 2. It's a DM conversation with this user
                        # 3. It's from a small group conversation (< 5 people in recent context)
                        if (str(r[1]) == str(user_id) or  # User sent this message
                            (r[5] == "dm" if len(r) > 5 else False)):  # It's a DM (could be with this user)
                            user_relevant.append({
                                "id": r[0], "user_id": r[1], "role": r[2],
                                "content": r[3], "timestamp": r[4],
                                "channel_type": r[5] if len(r) > 5 else "unknown"
                            })

                    # If we don't have enough user-specific memories, add some general ones
                    if len(user_relevant) < top_k:
                        general_memories = []
                        for r in all_results:
                            # Check if this result is already in user_relevant
                            already_included = any(ur["id"] == r[0] for ur in user_relevant)
                            if not already_included:
                                general_memories.append({
                                    "id": r[0], "user_id": r[1], "role": r[2],
                                    "content": r[3], "timestamp": r[4],
                                    "channel_type": r[5] if len(r) > 5 else "unknown"
                                })

                        # Add general memories to reach top_k
                        needed = top_k - len(user_relevant)
                        user_relevant.extend(general_memories[:needed])

                    conn.close()
                    return user_relevant[:top_k]  # Limit to requested amount

                except Exception as e:
                    logger.error(f"Error in personalized memory fetch: {e}", exc_info=True)
                    return []

            personalized_memories = await asyncio.to_thread(_fetch_and_filter)

            retrieval_duration = time.time() - start_time
            logger.info(f"Personalized memory retrieval completed in {retrieval_duration:.2f}s, found {len(personalized_memories)} user-relevant memories.")
            return personalized_memories

        except Exception as e:
            logger.error(f"Error during personalized memory retrieval: {e}", exc_info=True)
            return []

# Example Usage (for testing purposes, replace with actual integration)
async def main_test():
    logging.basicConfig(level=logging.INFO)
    retriever = RAGRetriever(max_entries=10) # Use small max_entries for testing

    # Simulate checking and building index if needed (e.g., in a background task)
    if retriever.needs_rebuild():
        logger.info("Index needs rebuild, building...")
        # In a real app, run this in a background task/thread
        retriever.build_index()
    else:
        logger.info("Index is up-to-date.")


    # Example query
    query = "Tell me about the discord setup"
    results = await retriever.retrieve(query, top_k=3)

    if results:
        print(f"\nRetrieved results for query: '{query}'")
        for result in results:
            print(f"  - ID: {result['id']}, Actor: {result['actor']}, Time: {result['timestamp']}, Content: {result['content'][:100]}...") # Print snippet
    else:
        print(f"\nNo results found for query: '{query}'")

if __name__ == "__main__":
    # Create dummy data if DB doesn't exist
    if not os.path.exists("conversation_logs.db"):
        print("Creating dummy conversation_logs.db for testing...")
        conn = sqlite3.connect("conversation_logs.db")
        conn.execute("""
            CREATE TABLE IF NOT EXISTS interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp REAL,
                actor TEXT,
                content TEXT
            )
        """)
        dummy_data = [
            (time.time() - 600, 'user', 'What is the plan for today?'),
            (time.time() - 540, 'bot', 'We need to finalize the report and then discuss the discord setup.'),
            (time.time() - 480, 'user', 'Okay, let\'s focus on the report first.'),
            (time.time() - 420, 'bot', 'Sounds good. I have the draft ready.'),
            (time.time() - 360, 'user', 'Can you remind me about the key points for the discord integration?'),
            (time.time() - 300, 'bot', 'We need to set up roles, channels, and integrate the notification bot.'),
            (time.time() - 240, 'user', 'Right, the notification bot is important.'),
            (time.time() - 180, 'bot', 'Indeed. It will ping users on new updates.'),
            (time.time() - 120, 'user', 'Let\'s also add a general chat channel.'),
            (time.time() - 60, 'bot', 'Good idea. Added to the list for the discord setup.'),
            (time.time(), 'user', 'Perfect, thanks!'),
        ]
        conn.executemany("INSERT INTO interactions (timestamp, actor, content) VALUES (?, ?, ?)", dummy_data)
        conn.commit()
        conn.close()
        print("Dummy database created.")

    asyncio.run(main_test())